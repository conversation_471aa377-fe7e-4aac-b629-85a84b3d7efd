<#
.SYNOPSIS
    Enterprise-grade multi-service development launcher for server monitoring system.

.DESCRIPTION
    This script provides a robust, enterprise-level solution for simultaneously launching
    Go server, Go client, and Vue.js frontend development services with comprehensive
    error handling, logging, and process management.

.PARAMETER ServerPort
    Port number for the Go server service (default: 7788)

.PARAMETER VuePort
    Port number for the Vue.js development server (default: 7799)

.PARAMETER Delay
    Delay in seconds between service startups (default: 3)

.PARAMETER SkipPortCheck
    Skip port availability checks before starting services

.PARAMETER LogLevel
    Logging verbosity level: Silent, Minimal, Normal, Verbose (default: Normal)

.PARAMETER ConfigFile
    Path to configuration file (optional)

.PARAMETER Help
    Display help information

.EXAMPLE
    .\test_three_client.ps1
    Start all services with default configuration

.EXAMPLE
    .\test_three_client.ps1 -ServerPort 8080 -VuePort 3000 -LogLevel Verbose
    Start services with custom ports and verbose logging

.NOTES
    Version:        2.0.0
    Author:         Development Team
    Creation Date:  2025-01-01
    Purpose:        Multi-service development environment launcher

.LINK
    https://github.com/your-org/server-monitor
#>

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Port number for the Go server service")]
    [ValidateRange(1024, 65535)]
    [int]$ServerPort = 7788,

    [Parameter(HelpMessage = "Port number for the Vue.js development server")]
    [ValidateRange(1024, 65535)]
    [int]$VuePort = 7799,

    [Parameter(HelpMessage = "Port number for the Go client service")]
    [ValidateRange(1024, 65535)]
    [int]$ClientPort = 7789,

    [Parameter(HelpMessage = "Delay in seconds between service startups")]
    [ValidateRange(0, 60)]
    [int]$Delay = 3,

    [Parameter(HelpMessage = "Skip port availability checks")]
    [switch]$SkipPortCheck,

    [Parameter(HelpMessage = "Logging verbosity level")]
    [ValidateSet("Silent", "Minimal", "Normal", "Verbose")]
    [string]$LogLevel = "Normal",

    [Parameter(HelpMessage = "Path to configuration file")]
    [ValidateScript({
        if ($_ -and -not (Test-Path $_)) {
            throw "Configuration file not found: $_"
        }
        return $true
    })]
    [string]$ConfigFile,

    [Parameter(HelpMessage = "Display help information")]
    [switch]$Help
)

# Script-level variables
$script:Config = @{
    Version = "2.0.0"
    StartTime = Get-Date
    Processes = [System.Collections.ArrayList]::new()
    LogLevel = $LogLevel
    ExitCode = 0
    ShutdownRequested = $false # New flag for graceful shutdown
}

# Service definitions
$script:Services = @{
    GoServer = @{
        Name = "Go Server"
        Command = "go"
        Arguments = "run main.go -s"
        WorkingDirectory = $PSScriptRoot
        Port = $ServerPort
        Required = $true
        HealthCheckPath = "/api/health"
    }
    GoClient = @{
        Name = "Go Client"
        Command = "go"
        Arguments = "run main.go -c"
        WorkingDirectory = $PSScriptRoot
        Port = $ClientPort
        Required = $false
        HealthCheckPath = $null
    }
    VueServer = @{
        Name = "Vue Development Server"
        Command = "pnpm"
        Arguments = "dev"
        WorkingDirectory = (Join-Path $PSScriptRoot "vue")
        Port = $VuePort
        Required = $false
        HealthCheckPath = "/"
    }
}

#region Logging Functions

<#
.SYNOPSIS
    Enhanced logging function with multiple levels and formatting
#>
function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,

        [Parameter(Position = 1)]
        [ValidateSet("Info", "Success", "Warning", "Error", "Debug", "Verbose")]
        [string]$Level = "Info",

        [Parameter()]
        [string]$Component = "MAIN",

        [Parameter()]
        [switch]$NoNewline
    )

    # Check if we should output based on log level
    $shouldOutput = switch ($script:Config.LogLevel) {
        "Silent" { $Level -eq "Error" }
        "Minimal" { $Level -in @("Error", "Warning", "Success") }
        "Normal" { $Level -in @("Error", "Warning", "Success", "Info") }
        "Verbose" { $true }
        default { $true }
    }

    if (-not $shouldOutput) { return }

    # Format timestamp
    $timestamp = Get-Date -Format "HH:mm:ss.fff"

    # Define colors and symbols
    $colorMap = @{
        "Info"    = @{ Color = "White"; Symbol = "[INFO]" }
        "Success" = @{ Color = "Green"; Symbol = "[OK]" }
        "Warning" = @{ Color = "Yellow"; Symbol = "[WARN]" }
        "Error"   = @{ Color = "Red"; Symbol = "[ERROR]" }
        "Debug"   = @{ Color = "Gray"; Symbol = "[DEBUG]" }
        "Verbose" = @{ Color = "DarkGray"; Symbol = "[VERBOSE]" }
    }

    $config = $colorMap[$Level]
    $symbol = $config.Symbol
    $color = $config.Color

    # Format message
    $formattedMessage = "[$timestamp] $symbol [$Component] $Message"

    if ($NoNewline) {
        Write-Host $formattedMessage -ForegroundColor $color -NoNewline
    } else {
        Write-Host $formattedMessage -ForegroundColor $color
    }
}

<#
.SYNOPSIS
    Write a section header with decorative formatting
#>
function Write-SectionHeader {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Title,

        [Parameter()]
        [string]$BorderChar = "="
    )

    $border = $BorderChar * ($Title.Length + 4)
    Write-Host ""
    Write-Log $border -Level "Info"
    Write-Log "  $Title  " -Level "Info"
    Write-Log $border -Level "Info"
    Write-Host ""
}

#endregion

#region Configuration and Help

<#
.SYNOPSIS
    Load configuration from file if specified
#>
function Initialize-Configuration {
    [CmdletBinding()]
    param()

    if ($ConfigFile -and (Test-Path $ConfigFile)) {
        try {
            $configData = Get-Content $ConfigFile | ConvertFrom-Json
            Write-Log "Configuration loaded from: $ConfigFile" -Level "Success" -Component "CONFIG"

            # Override parameters with config file values if not explicitly set
            if ($configData.ServerPort -and $PSBoundParameters.Keys -notcontains 'ServerPort') {
                $script:Services.GoServer.Port = $configData.ServerPort
            }
            if ($configData.VuePort -and $PSBoundParameters.Keys -notcontains 'VuePort') {
                $script:Services.VueServer.Port = $configData.VuePort
            }
            if ($configData.ClientPort -and $PSBoundParameters.Keys -notcontains 'ClientPort') {
                $script:Services.GoClient.Port = $configData.ClientPort
            }
            if ($configData.Delay -and $PSBoundParameters.Keys -notcontains 'Delay') {
                $script:Delay = $configData.Delay
            }
        } catch {
            Write-Log "Failed to load configuration file: $($_.Exception.Message)" -Level "Warning" -Component "CONFIG"
        }
    }
}

<#
.SYNOPSIS
    Display comprehensive help information
#>
function Show-Help {
    $helpText = @"

Enterprise Multi-Service Development Launcher v$($script:Config.Version)

DESCRIPTION:
    Launches Go server, Go client, and Vue.js development services simultaneously
    with enterprise-grade process management, logging, and error handling.

USAGE:
    .\test_three_client.ps1 [OPTIONS]

OPTIONS:
    -ServerPort <int>     Go server port (default: 7788, range: 1024-65535)
    -VuePort <int>        Vue development server port (default: 7799, range: 1024-65535)
    -ClientPort <int>     Go client port (default: 7789, range: 1024-65535)
    -Delay <int>          Startup delay between services in seconds (default: 3, range: 0-60)
    -SkipPortCheck        Skip port availability validation before startup
    -LogLevel <string>    Logging verbosity: Silent|Minimal|Normal|Verbose (default: Normal)
    -ConfigFile <path>    Load configuration from JSON file
    -Help                 Display this help information

EXAMPLES:
    # Basic usage with defaults
    .\test_three_client.ps1

    # Custom ports with verbose logging
    .\test_three_client.ps1 -ServerPort 8080 -VuePort 3000 -LogLevel Verbose

    # Quick startup with minimal logging
    .\test_three_client.ps1 -Delay 0 -LogLevel Minimal -SkipPortCheck

    # Load from configuration file
    .\test_three_client.ps1 -ConfigFile ".\dev-config.json"

CONFIGURATION FILE FORMAT:
    {
        "ServerPort": 8080,
        "VuePort": 3000,
        "ClientPort": 7789,
        "Delay": 5,
        "LogLevel": "Verbose"
    }

SERVICES:
    Go Server             http://localhost:$($script:Services.GoServer.Port)
    Vue Development       http://localhost:$($script:Services.VueServer.Port)

For more information, visit: https://github.com/your-org/server-monitor

"@

    Write-Host $helpText -ForegroundColor Cyan
    exit 0
}

if ($Help) {
    Show-Help
}

#endregion

#region Utility Functions

<#
.SYNOPSIS
    Test if a network port is available
#>
function Test-PortAvailability {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [int]$Port,

        [Parameter()]
        [string]$ComputerName = "localhost",

        [Parameter()]
        [int]$TimeoutMs = 1000
    )

    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect($ComputerName, $Port, $null, $null)
        $waitHandle = $asyncResult.AsyncWaitHandle

        if ($waitHandle.WaitOne($TimeoutMs)) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true  # Port is open/occupied
        } else {
            $tcpClient.Close()
            return $false  # Port is closed/available
        }
    } catch {
        return $false  # Port is available (connection failed)
    }
}

<#
.SYNOPSIS
    Validate system prerequisites
#>
function Test-Prerequisites {
    [CmdletBinding()]
    param()

    $prerequisites = @(
        @{ Name = "Go"; Command = "go version"; Component = "GO" }
        @{ Name = "pnpm"; Command = "pnpm --version"; Component = "PNPM" }
    )

    $allValid = $true

    foreach ($prereq in $prerequisites) {
        try {
            $null = Invoke-Expression $prereq.Command 2>$null
            Write-Log "$($prereq.Name) is available" -Level "Success" -Component $prereq.Component
        } catch {
            Write-Log "$($prereq.Name) is not installed or not in PATH" -Level "Error" -Component $prereq.Component
            $allValid = $false
        }
    }

    return $allValid
}

<#
.SYNOPSIS
    Perform comprehensive port validation
#>
function Test-PortConfiguration {
    [CmdletBinding()]
    param()

    if ($SkipPortCheck) {
        Write-Log "Port validation skipped by user request" -Level "Warning" -Component "PORT"
        return $true
    }

    $portsToCheck = @()
    foreach ($service in $script:Services.Values) {
        if ($service.Port -gt 0) {
            $portsToCheck += @{ Port = $service.Port; Service = $service.Name }
        }
    }

    $conflicts = @()
    foreach ($portInfo in $portsToCheck) {
        if (Test-PortAvailability -Port $portInfo.Port) {
            $conflicts += $portInfo
            Write-Log "Port $($portInfo.Port) is already in use (required for $($portInfo.Service))" -Level "Warning" -Component "PORT"
        } else {
            Write-Log "Port $($portInfo.Port) is available for $($portInfo.Service)" -Level "Success" -Component "PORT"
        }
    }

    if ($conflicts.Count -gt 0) {
        Write-Log "Found $($conflicts.Count) port conflict(s). Services may fail to start." -Level "Warning" -Component "PORT"
        return $false
    }

    return $true
}

#endregion

#region Process Management

<#
.SYNOPSIS
    Enhanced process management with comprehensive monitoring
#>
class ServiceProcess {
    [string]$Name
    [System.Diagnostics.Process]$Process
    [datetime]$StartTime
    [int]$Port
    [string]$HealthCheckPath
    [bool]$IsHealthy

    ServiceProcess([string]$name, [System.Diagnostics.Process]$process, [int]$port, [string]$healthCheckPath) {
        $this.Name = $name
        $this.Process = $process
        $this.StartTime = Get-Date
        $this.Port = $port
        $this.HealthCheckPath = $healthCheckPath
        $this.IsHealthy = $false
    }

    [bool] IsRunning() {
        return $this.Process -and -not $this.Process.HasExited
    }

    [void] Stop() {
        if ($this.IsRunning()) {
            try {
                $this.Process.Kill()
                $this.Process.WaitForExit(5000)
            } catch {
                Write-Log "Failed to stop process $($this.Process.Id): $($_.Exception.Message)" -Level "Warning" -Component "PROCESS"
            }
        }
    }

    [timespan] GetUptime() {
        if ($this.IsRunning()) {
            return (Get-Date) - $this.StartTime
        }
        return [timespan]::Zero
    }
}

<#
.SYNOPSIS
    Start a service process with comprehensive monitoring
#>
function Start-ServiceProcess {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfig,

        [Parameter()]
        [int]$StartupTimeoutSeconds = 30
    )

    $serviceName = $ServiceConfig.Name
    Write-Log "Starting $serviceName..." -Level "Info" -Component "SERVICE"

    try {
        # Create process start info
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = $ServiceConfig.Command
        $startInfo.Arguments = $ServiceConfig.Arguments
        $startInfo.WorkingDirectory = $ServiceConfig.WorkingDirectory
        $startInfo.UseShellExecute = $false
        $startInfo.RedirectStandardOutput = $true
        $startInfo.RedirectStandardError = $true
        $startInfo.CreateNoWindow = $true

        # Create and configure process
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $startInfo

        # Set up output handling with proper component tagging
        $outputHandler = {
            param($proc, $e)
            if ($e.Data) {
                Write-Log $e.Data -Level "Verbose" -Component $serviceName.ToUpper()
            }
        }

        $errorHandler = {
            param($proc, $e)
            if ($e.Data) {
                Write-Log $e.Data -Level "Warning" -Component $serviceName.ToUpper()
            }
        }

        $process.add_OutputDataReceived($outputHandler)
        $process.add_ErrorDataReceived($errorHandler)

        # Start the process
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()

        # Create service process wrapper
        $serviceProcess = [ServiceProcess]::new(
            $serviceName,
            $process,
            $ServiceConfig.Port,
            $ServiceConfig.HealthCheckPath
        )

        # Add to process collection
        $script:Config.Processes.Add($serviceProcess) | Out-Null

        Write-Log "$serviceName started successfully (PID: $($process.Id))" -Level "Success" -Component "SERVICE"

        # Wait for port availability if specified
        if ($ServiceConfig.Port -gt 0) {
            Write-Log "Waiting for $serviceName to become available on port $($ServiceConfig.Port)..." -Level "Info" -Component "SERVICE"

            $timeout = $StartupTimeoutSeconds
            $elapsed = 0

            while ($elapsed -lt $timeout) {
                if (Test-PortAvailability -Port $ServiceConfig.Port) {
                    Write-Log "$serviceName is now available on port $($ServiceConfig.Port)" -Level "Success" -Component "SERVICE"
                    $serviceProcess.IsHealthy = $true
                    break
                }
                Start-Sleep -Seconds 1
                $elapsed++
            }

            if ($elapsed -ge $timeout) {
                Write-Log "$serviceName did not become available within $timeout seconds" -Level "Warning" -Component "SERVICE"
            }
        }

        return $serviceProcess

    } catch {
        Write-Log "Failed to start $serviceName`: $($_.Exception.Message)" -Level "Error" -Component "SERVICE"
        return $null
    }
}

<#
.SYNOPSIS
    Stop all running service processes gracefully
#>
function Stop-AllServices {
    [CmdletBinding()]
    param(
        [Parameter()]
        [int]$TimeoutSeconds = 10
    )

    if ($script:Config.Processes.Count -eq 0) {
        Write-Log "No services to stop" -Level "Info" -Component "CLEANUP"
        return
    }

    Write-Log "Stopping $($script:Config.Processes.Count) service(s)..." -Level "Info" -Component "CLEANUP"

    foreach ($serviceProcess in $script:Config.Processes) {
        if ($serviceProcess.IsRunning()) {
            try {
                Write-Log "Stopping $($serviceProcess.Name) (PID: $($serviceProcess.Process.Id))..." -Level "Info" -Component "CLEANUP"
                $serviceProcess.Stop()
                Write-Log "$($serviceProcess.Name) stopped successfully" -Level "Success" -Component "CLEANUP"
            } catch {
                Write-Log "Failed to stop $($serviceProcess.Name): $($_.Exception.Message)" -Level "Warning" -Component "CLEANUP"
            }
        }
    }

    # Clear the process collection
    $script:Config.Processes.Clear()
    Write-Log "All services stopped" -Level "Success" -Component "CLEANUP"
}

<#
.SYNOPSIS
    Display service status summary
#>
function Show-ServiceStatus {
    [CmdletBinding()]
    param()

    if ($script:Config.Processes.Count -eq 0) {
        Write-Log "No services are currently running" -Level "Info" -Component "STATUS"
        return
    }

    Write-SectionHeader "Service Status Summary"

    foreach ($serviceProcess in $script:Config.Processes) {
        $status = if ($serviceProcess.IsRunning()) { "Running" } else { "Stopped" }
        $uptime = $serviceProcess.GetUptime()
        $health = if ($serviceProcess.IsHealthy) { "Healthy" } else { "Unknown" }

        $statusLine = "$($serviceProcess.Name): $status"
        if ($serviceProcess.Port -gt 0) {
            $statusLine += " (Port: $($serviceProcess.Port))"
        }
        $statusLine += " | Uptime: $($uptime.ToString('hh\:mm\:ss')) | Health: $health"

        $level = if ($serviceProcess.IsRunning()) { "Success" } else { "Warning" }
        Write-Log $statusLine -Level $level -Component "STATUS"
    }
}

#endregion

#region Signal Handling and Cleanup

<#
.SYNOPSIS
    Register cleanup handlers for graceful shutdown
#>
function Register-CleanupHandlers {
    [CmdletBinding()]
    param()

    # Register PowerShell exit handler
    $null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
        Write-Log "PowerShell exiting, cleaning up services..." -Level "Info" -Component "CLEANUP"
        Stop-AllServices
    }

    # Register Ctrl+C handler
    # Ensure Ctrl+C is treated as an interrupt, not input
    [Console]::TreatControlCAsInput = $false
    # Register a handler for the Ctrl+C event
    $null = [Console]::Add_CancelKeyPress({
        param($sender, $e)
        # Prevent the process from terminating immediately
        $e.Cancel = $true
        Write-Log "Interrupt signal received (Ctrl+C). Initiating graceful shutdown..." -Level "Info" -Component "CLEANUP"
        $script:Config.ShutdownRequested = $true
        # The script will exit naturally after the event handler completes and main logic finishes
    })

    Write-Log "Cleanup handlers registered" -Level "Debug" -Component "INIT"
}

#endregion

#region Main Application Logic

<#
.SYNOPSIS
    Main application entry point with comprehensive orchestration
#>
function Start-DevelopmentEnvironment {
    [CmdletBinding()]
    param()

    try {
        # Initialize configuration
        Initialize-Configuration

        # Display startup banner
        Write-SectionHeader "Enterprise Multi-Service Development Launcher v$($script:Config.Version)"
        Write-Log "Session started at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -Level "Info" -Component "MAIN"
        Write-Log "Log Level: $($script:Config.LogLevel)" -Level "Info" -Component "MAIN"

        # Validate prerequisites
        Write-Log "Validating system prerequisites..." -Level "Info" -Component "MAIN"
        if (-not (Test-Prerequisites)) {
            Write-Log "Prerequisites validation failed. Please install missing tools and try again." -Level "Error" -Component "MAIN"
            $script:Config.ExitCode = 1
            return
        }

        # Validate port configuration
        Write-Log "Validating port configuration..." -Level "Info" -Component "MAIN"
        $portValidation = Test-PortConfiguration
        if (-not $portValidation -and -not $SkipPortCheck) {
            Write-Log "Port validation completed with warnings. Services may experience conflicts." -Level "Warning" -Component "MAIN"
        }

        # Register cleanup handlers
        Register-CleanupHandlers

        # Start services in sequence
        Write-SectionHeader "Starting Development Services"

        $serviceOrder = @("GoServer", "GoClient", "VueServer")
        $startedServices = 0

        foreach ($serviceName in $serviceOrder) {
            $serviceConfig = $script:Services[$serviceName]

            # Apply runtime port configuration
            if ($serviceName -eq "GoServer") {
                $serviceConfig.Port = $ServerPort
            } elseif ($serviceName -eq "VueServer") {
                $serviceConfig.Port = $VuePort
            } elseif ($serviceName -eq "GoClient") {
                $serviceConfig.Port = $ClientPort
            }

            $serviceProcess = Start-ServiceProcess -ServiceConfig $serviceConfig

            if ($serviceProcess) {
                $startedServices++

                # Add delay between services (except for the last one)
                if ($serviceName -ne $serviceOrder[-1] -and $Delay -gt 0) {
                    Write-Log "Waiting $Delay seconds before starting next service..." -Level "Info" -Component "MAIN"
                    Start-Sleep -Seconds $Delay
                }
            } else {
                Write-Log "Failed to start $($serviceConfig.Name)" -Level "Error" -Component "MAIN"
                if ($serviceConfig.Required) {
                    Write-Log "Required service failed to start. Aborting startup sequence." -Level "Error" -Component "MAIN"
                    $script:Config.ExitCode = 1
                    return
                }
            }
        }

        # Display startup summary
        Write-SectionHeader "Startup Complete"
        Write-Log "Successfully started $startedServices out of $($serviceOrder.Count) services" -Level "Success" -Component "MAIN"

        # Show service status
        Show-ServiceStatus

        # Display access URLs
        Write-Host ""
        Write-Log "Service Access URLs:" -Level "Info" -Component "MAIN"
        Write-Log "  Go Server:     http://localhost:$ServerPort" -Level "Info" -Component "MAIN"
        Write-Log "  Go Client:     (No direct URL, runs on port $ClientPort)" -Level "Info" -Component "MAIN"
        Write-Log "  Vue Frontend:  http://localhost:$VuePort" -Level "Info" -Component "MAIN"
        Write-Log "" -Level "Info"

        # The script will now run until interrupted by Ctrl+C or other means.
        # The CancelKeyPress handler will manage cleanup.
        Write-Log "Development environment running. Press Ctrl+C to stop all services and exit..." -Level "Info" -Component "MAIN"

        # Keep the script alive until interrupted or shutdown is requested
        while (-not $script:Config.ShutdownRequested) {
            Start-Sleep -Seconds 1
        }
        Write-Log "Shutdown requested. Performing final cleanup..." -Level "Info" -Component "MAIN"
        Stop-AllServices # Call Stop-AllServices in the main thread

    } catch {
        Write-Log "Unexpected error in main application: $($_.Exception.Message)" -Level "Error" -Component "MAIN"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level "Debug" -Component "MAIN"
        $script:Config.ExitCode = 1
    } finally {
        # Final cleanup and logging
        $endTime = Get-Date
        $duration = $endTime - $script:Config.StartTime
        Write-Log "Session duration: $($duration.ToString('hh\:mm\:ss'))" -Level "Info" -Component "MAIN"
        Write-Log "Development environment stopped" -Level "Success" -Component "MAIN"
    }
}

#endregion

#region Script Entry Point

<#
.SYNOPSIS
    Script entry point with comprehensive error handling
#>

# Set error action preference for better error handling
$ErrorActionPreference = "Stop"

# Initialize and run the development environment
try {
    Start-DevelopmentEnvironment
} catch {
    Write-Log "Critical error occurred: $($_.Exception.Message)" -Level "Error" -Component "MAIN"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level "Debug" -Component "MAIN"
    $script:Config.ExitCode = 1
} finally {
    # Ensure cleanup always happens if not already handled by Ctrl+C
    if (-not $script:Config.ShutdownRequested -and $script:Config.Processes.Count -gt 0) {
        Stop-AllServices
    }
    # Exit with appropriate code
    exit $script:Config.ExitCode
}

#endregion
