export const APP_CONFIG = {
  // Polling intervals in milliseconds
  DASHBOARD_DATA_FETCH_INTERVAL: 60000, // 60 seconds
  TIME_UPDATE_INTERVAL: 1000, // 1 second
  SERVICES_DATA_FETCH_INTERVAL: 60000, // 60 seconds
  SYSTEM_STATS_HTTP_POLLING_INTERVAL: 30000, // 30 seconds

  // Thresholds for server status and progress bars
  CPU_WARNING_THRESHOLD: 85, // Used in servers.js
  CPU_ORANGE_THRESHOLD: 60, // Used in DashboardView.vue
  MEMORY_WARNING_THRESHOLD: 90, // Used in servers.js
  MEMORY_ORANGE_THRESHOLD: 60, // Used in DashboardView.vue

  // WebSocket reconnection
  WEBSOCKET_OFFLINE_THRESHOLD_SECONDS: 60, // Used in servers.js for lastActive check

  // Log viewer window dimensions
  LOG_VIEWER_WIDTH: 800,
  LOG_VIEWER_HEIGHT: 600,

  // Uptime formatting thresholds (in seconds)
  UPTIME_MINUTE_THRESHOLD: 60,
  UPTIME_HOUR_THRESHOLD: 3600,
  UPTIME_DAY_THRESHOLD: 86400,
};
