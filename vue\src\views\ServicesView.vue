<template>
  <div
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <router-link
                to="/dashboard"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</router-link
              >
              <router-link
                to="/services"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</router-link
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ currentTime }}</span>
            <button
              @click="logout"
              class="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              title="注销"
            >
              注销
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 relative">
      <div v-if="isLoadingData" class="loading-overlay">
        <div class="spinner"></div>
        <p class="text-white mt-4">加载中...</p>
      </div>
      <div class="px-4 py-6 sm:px-0" :class="{ 'opacity-50': isLoadingData }">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务管理</h2>
              <p class="mt-2 text-gray-600" id="server-info">
                {{ serverInfo }}
              </p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  type="text"
                  v-model="debouncedSearchQuery"
                  placeholder="搜索服务名称..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                  :disabled="isLoadingData"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <!-- 服务类型过滤 -->
              <select
                v-model="serviceTypeFilter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :disabled="isLoadingData"
              >
                <option value="all">所有服务</option>
                <option value="supervisor">Supervisor</option>
                <option value="systemd">Systemd</option>
                <option value="docker">Docker</option>
              </select>
              <!-- 状态过滤 -->
              <select
                v-model="statusFilter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :disabled="isLoadingData"
              >
                <option value="all">所有状态</option>
                <option value="running">运行中</option>
                <option value="stopped">已停止</option>
                <option value="failed">失败</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                @click="refreshServices"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                :disabled="isLoadingData"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- CPU 使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">CPU 使用率</h3>
              <div
                class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900"
                >{{ systemStats.cpu.toFixed(2) }}%</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar"
                :style="{ width: systemStats.cpu + '%' }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">实时数据</p>
          </div>

          <!-- 内存使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">内存使用率</h3>
              <div
                class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900"
                >{{ systemStats.memory.toFixed(2) }}%</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar"
                :style="{ width: systemStats.memory + '%' }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">
              {{ formatBytes(systemStats.memUsed) }} /
              {{ formatBytes(systemStats.memTotal) }}
            </p>
          </div>
        </div>

        <!-- 第二行：磁盘使用率 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 磁盘使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">磁盘使用率</h3>
              <div
                class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-yellow-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900"
                >{{ systemStats.disk.toFixed(2) }}%</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar"
                :style="{ width: systemStats.disk + '%' }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">
              {{ formatBytes(systemStats.diskUsed) }} /
              {{ formatBytes(systemStats.diskTotal) }}
            </p>
          </div>

          <!-- 网络流量 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">网络流量</h3>
              <div
                class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600"
                  >↑ {{ formatBytes(systemStats.netOutTransfer) }}</span
                >
                <span class="text-gray-600"
                  >↓ {{ formatBytes(systemStats.netInTransfer) }}</span
                >
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full progress-bar"
                style="width: 30%"
              ></div>
            </div>
            <p class="text-sm text-gray-600">累计传输量</p>
          </div>
        </div>

        <!-- 服务管理标签页 -->
        <div class="bg-white rounded-lg shadow-md">
          <!-- 标签页头部 -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              <button
                @click="switchTab('supervisor')"
                :class="[
                  'tab-button',
                  'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                  currentTab === 'supervisor'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                ]"
                :disabled="isLoadingData"
              >
                Supervisor 服务
              </button>
              <button
                @click="switchTab('systemd')"
                :class="[
                  'tab-button',
                  'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                  currentTab === 'systemd'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                ]"
                :disabled="isLoadingData"
              >
                Systemd 服务
              </button>
              <button
                @click="switchTab('docker')"
                :class="[
                  'tab-button',
                  'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                  currentTab === 'docker'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                ]"
                :disabled="isLoadingData"
              >
                Docker 容器
              </button>
            </nav>
          </div>

          <!-- 标签页内容 -->
          <div class="p-6">
            <div
              class="grid grid-cols-1 lg:grid-cols-2 gap-6"
              v-if="filteredServices.length > 0"
            >
              <div
                v-for="service in filteredServices"
                :key="service.name"
                class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center">
                    <span
                      :class="[
                        'status-indicator',
                        getServiceStatusClass(service.status),
                      ]"
                    ></span>
                    <h3 class="text-lg font-semibold text-gray-900">
                      {{ service.name }}
                    </h3>
                  </div>
                  <span
                    :class="[
                      'text-sm font-medium',
                      getServiceStatusTextClass(service.status),
                    ]"
                    >{{ getServiceStatusText(service.status) }}</span
                  >
                </div>

                <p class="text-sm text-gray-600 mb-3">
                  {{ service.description || '无描述' }}
                </p>

                <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                  <div>
                    <span class="font-medium text-gray-700">PID:</span>
                    <span class="text-gray-600">{{ service.pid || '--' }}</span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">运行时间:</span>
                    <span class="text-gray-600">{{
                      service.uptime
                    }}</span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">内存:</span>
                    <span class="text-gray-600">{{
                      service.memory || '--'
                    }}</span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">CPU:</span>
                    <span class="text-gray-600">{{ service.cpu || '--' }}</span>
                  </div>
                </div>

                <div class="flex space-x-2">
                  <button
                    v-if="service.status !== 'running'"
                    @click="startService(service.name, currentTab)"
                    class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                    :disabled="serviceActionLoading[`start-${service.name}`]"
                  >
                    启动
                  </button>
                  <button
                    v-if="service.status === 'running'"
                    @click="stopService(service.name, currentTab)"
                    class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                    :disabled="serviceActionLoading[`stop-${service.name}`]"
                  >
                    停止
                  </button>
                  <button
                    v-if="service.status === 'running'"
                    @click="restartService(service.name, currentTab)"
                    class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                    :disabled="serviceActionLoading[`restart-${service.name}`]"
                  >
                    重启
                  </button>
                  <button
                    @click="viewLogs(service.name, currentTab)"
                    class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                    :disabled="isLoadingData"
                  >
                    日志
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="col-span-full text-center py-12 text-gray-500">
              {{
                currentServices[currentTab]?.length === 0
                  ? `没有找到 ${currentTab} 服务。`
                  : '没有符合筛选条件的服务。'
              }}
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import axios from 'axios';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useWebSocket } from '../composables/useWebSocket';
import { useServicesStore } from '../store/services';
import { formatUptime, formatBytes } from '../utils/formatters';
import { APP_CONFIG } from '../config/appConfig';
import { debounce } from '../utils/debounce';

dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const route = useRoute();
const router = useRouter();
const servicesStore = useServicesStore();

const currentServerId = ref(null);
const currentServerName = ref('Unknown Server');
const serverInfo = ref('管理 Supervisor、Systemd、Docker 服务');
const currentTime = ref('');

const searchQuery = ref('');
const debouncedSearchQuery = ref(''); // New ref for debounced search input
const serviceTypeFilter = ref('all');
const statusFilter = ref('all');
const currentTab = ref('supervisor');

const serviceFetchInterval = ref(null);
const timeUpdateInterval = ref(null);
const httpPollingInterval = ref(null);

// Reactive states for service action loading
const serviceActionLoading = ref({});

const { proxy } = getCurrentInstance();

const {
  ws,
  isConnected,
  reconnectAttempts,
  error,
  connect,
  disconnect,
  sendMessage,
} = useWebSocket(
  `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/frontend`,
  {
    onOpen: () => {
      console.log('WebSocket connected');
      // Authenticate and subscribe to the current server
      sendMessage(
        JSON.stringify({
          type: 'auth',
          timestamp: new Date().toISOString(),
        })
      );
    },
    onMessage: event => {
      try {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    },
    onReconnectFail: () => {
      console.log(
        'Max reconnection attempts reached for WebSocket, falling back to HTTP polling'
      );
      startHttpPolling();
    },
    onError: err => {
      console.error('WebSocket error:', err);
      // No explicit action needed here, onclose will handle reconnection/fallback
    },
  }
);

// Computed properties for filtered services
// Debounce the search query update
const debouncedUpdateSearchQuery = debounce(value => {
  searchQuery.value = value;
}, 300);

watch(debouncedSearchQuery, newValue => {
  debouncedUpdateSearchQuery(newValue);
});

const filteredServices = computed(() => {
  return servicesStore.searchServices(
    currentTab.value,
    searchQuery.value,
    statusFilter.value
  );
});

const systemStats = computed(() => servicesStore.systemStats);

// Combined loading state for the page
const isLoadingData = computed(() => {
  return (
    servicesStore.loading[currentTab.value] ||
    servicesStore.loading.stats ||
    Object.values(serviceActionLoading.value).some(status => status)
  );
});

// Methods (formatUptime and formatBytes are now imported from formatters.js)

const getServiceStatusClass = status => {
  return {
    'status-running': status === 'running' || status === 'active',
    'status-stopped': status === 'stopped' || status === 'inactive',
    'status-failed': status === 'failed' || status === 'error',
    'status-unknown':
      status !== 'running' &&
      status !== 'active' &&
      status !== 'stopped' &&
      status !== 'inactive' &&
      status !== 'failed' &&
      status !== 'error',
  };
};

const getServiceStatusText = status => {
  switch (status) {
    case 'running':
    case 'active':
      return '运行中';
    case 'stopped':
    case 'inactive':
      return '已停止';
    case 'failed':
    case 'error':
      return '失败';
    default:
      return '未知';
  }
};

const getServiceStatusTextClass = status => {
  return {
    'text-green-600': status === 'running' || status === 'active',
    'text-red-600': status === 'stopped' || status === 'inactive',
    'text-yellow-600': status === 'failed' || status === 'error',
    'text-gray-600':
      status !== 'running' &&
      status !== 'active' &&
      status !== 'stopped' &&
      status !== 'inactive' &&
      status !== 'failed' &&
      status !== 'error',
  };
};

const logout = () => {
  router.push('/login');
};

const switchTab = async tab => {
  currentTab.value = tab;
  await fetchServices(tab);
};

const startService = async (name, type) => {
  if (!currentServerId.value) {
    ElMessage.warning('请先选择一个服务器。');
    return;
  }
  serviceActionLoading.value[`start-${name}`] = true;
  try {
    const response = await proxy.$axios.post(
      `/api/services/${type}/start`,
      {
        serverId: parseInt(currentServerId.value),
        serviceName: name,
        serviceType: type,
      },
      { withCredentials: true }
    );
    if (response.status === 200) {
      ElMessage.success(`成功启动 ${type} 服务: ${name}`);
      await fetchServices(type);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error starting service:', error);
    ElMessage.error(`启动服务失败: ${error.message}`);
  } finally {
    serviceActionLoading.value[`start-${name}`] = false;
  }
};

const stopService = async (name, type) => {
  if (!currentServerId.value) {
    ElMessage.warning('请先选择一个服务器。');
    return;
  }
  serviceActionLoading.value[`stop-${name}`] = true;
  try {
    const response = await proxy.$axios.post(
      `/api/services/${type}/stop`,
      {
        serverId: parseInt(currentServerId.value),
        serviceName: name,
        serviceType: type,
      },
      { withCredentials: true }
    );
    if (response.status === 200) {
      ElMessage.success(`成功停止 ${type} 服务: ${name}`);
      await fetchServices(type);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error stopping service:', error);
    ElMessage.error(`停止服务失败: ${error.message}`);
  } finally {
    serviceActionLoading.value[`stop-${name}`] = false;
  }
};

const restartService = async (name, type) => {
  if (!currentServerId.value) {
    ElMessage.warning('请先选择一个服务器。');
    return;
  }
  serviceActionLoading.value[`restart-${name}`] = true;
  try {
    const response = await proxy.$axios.post(
      `/api/services/${type}/restart`,
      {
        serverId: parseInt(currentServerId.value),
        serviceName: name,
        serviceType: type,
      },
      { withCredentials: true }
    );
    if (response.status === 200) {
      ElMessage.success(`成功重启 ${type} 服务: ${name}`);
      await fetchServices(type);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error restarting service:', error);
    ElMessage.error(`重启服务失败: ${error.message}`);
  } finally {
    serviceActionLoading.value[`restart-${name}`] = false;
  }
};

const viewLogs = async (name, type) => {
  if (!currentServerId.value) {
    ElMessage.warning('请先选择一个服务器。');
    return;
  }
  try {
    const response = await proxy.$axios.get(
      `/api/services/logs?serverId=${currentServerId.value}&serviceName=${name}&serviceType=${type}`,
      { withCredentials: true }
    );
    if (response.status === 200) {
      const logs = response.data;
      const logWindow = window.open(
        '',
        '_blank',
        `width=${APP_CONFIG.LOG_VIEWER_WIDTH},height=${APP_CONFIG.LOG_VIEWER_HEIGHT}`
      );
      logWindow.document.write(`
          <html>
            <head>
              <title>${type} 服务日志 - ${name} (${currentServerName.value})</title>
              <style>
                body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #00ff00; }
                pre { white-space: pre-wrap; word-wrap: break-word; }
              </style>
            </head>
            <body>
              <h3>${type} 服务日志 - ${name}</h3>
              <p>服务器: ${currentServerName.value} (ID: ${currentServerId.value})</p>
              <hr>
              <pre>${logs}</pre>
            </body>
          </html>
        `);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error viewing logs:', error);
    ElMessage.error(`查看日志失败: ${error.message}`);
  }
};

const fetchServices = async type => {
  if (!currentServerId.value) {
    servicesStore.services.value[type] = [];
    return;
  }
  const isAuthenticated = await servicesStore.fetchServices(
    type,
    currentServerId.value
  );
  if (!isAuthenticated) {
    router.push('/login');
  }
};

const refreshServices = async () => {
  await fetchServices(currentTab.value);
};

const fetchSystemStats = async () => {
  if (!currentServerId.value) {
    servicesStore.systemStats = {
      cpu: 0,
      memory: 0,
      memUsed: 0,
      memTotal: 0,
      disk: 0,
      diskUsed: 0,
      diskTotal: 0,
      netInTransfer: 0,
      netOutTransfer: 0,
    };
    return;
  }
  await servicesStore.fetchSystemStats(currentServerId.value);
};

const startHttpPolling = () => {
  console.log('Starting HTTP polling fallback for system stats');
  httpPollingInterval.value = setInterval(() => {
    fetchSystemStats();
  }, APP_CONFIG.SYSTEM_STATS_HTTP_POLLING_INTERVAL);
};

const handleWebSocketMessage = message => {
  switch (message.type) {
    case 'auth_response':
      if (message.error) {
        console.error('WebSocket auth failed:', message.error);
        startHttpPolling();
      } else {
        console.log('WebSocket authenticated:', message.data);
        if (currentServerId.value) {
          sendMessage(
            JSON.stringify({
              type: 'subscribe',
              server_id: parseInt(currentServerId.value),
              timestamp: new Date().toISOString(),
            })
          );
        }
      }
      break;
    case 'subscribe_response':
      console.log('Subscribed to server:', message.server_id);
      break;
    case 'system_stats_broadcast':
      if (message.server_id === parseInt(currentServerId.value)) {
        servicesStore.updateSystemStats(message.data);
      }
      break;
    case 'pong':
      break;
    default:
      console.log('Unknown WebSocket message type:', message.type);
  }
};

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// Watch for changes in route params (mid)
watch(
  () => route.query.mid,
  newMid => {
    currentServerId.value = newMid;
    currentServerName.value = route.query.server || 'Unknown Server';
    if (currentServerId.value && currentServerName.value) {
      serverInfo.value = `正在管理服务器: ${decodeURIComponent(currentServerName.value)} (ID: ${currentServerId.value})`;
      document.title = `服务管理 - ${decodeURIComponent(currentServerName.value)} - 服务器监控系统`;
    } else {
      serverInfo.value = `请从仪表板选择服务器以管理服务。`;
      document.title = `服务管理 - 服务器监控系统`;
    }
    // Re-fetch data when server ID changes
    fetchSystemStats();
    fetchServices(currentTab.value);
    // Re-initialize WebSocket subscription if server ID changes
    if (isConnected.value) {
      // Check if WebSocket is connected
      sendMessage(
        JSON.stringify({
          type: 'unsubscribe', // Unsubscribe from previous server
          server_id: parseInt(currentServerId.value), // Use old ID if available, or a dummy
          timestamp: new Date().toISOString(),
        })
      );
      sendMessage(
        JSON.stringify({
          type: 'subscribe', // Subscribe to new server
          server_id: parseInt(currentServerId.value),
          timestamp: new Date().toISOString(),
        })
      );
    }
  },
  { immediate: true } // Run immediately on component mount
);

// Lifecycle hooks
onMounted(async () => {
  console.log('Services component mounted');
  servicesStore.setCurrentServerId(currentServerId.value); // Set current server ID in store

  // Initial data fetch
  await fetchSystemStats();
  await fetchServices(currentTab.value);

  // Set up intervals
  serviceFetchInterval.value = setInterval(async () => {
    await fetchServices(currentTab.value);
  }, APP_CONFIG.SERVICES_DATA_FETCH_INTERVAL); // Every 30 seconds for service list

  timeUpdateInterval.value = setInterval(
    updateTime,
    APP_CONFIG.TIME_UPDATE_INTERVAL
  ); // Every second for current time

  // Initialize WebSocket for real-time stats
  connect();
});

onUnmounted(() => {
  console.log('Services component unmounted');
  if (serviceFetchInterval.value) clearInterval(serviceFetchInterval.value);
  if (timeUpdateInterval.value) clearInterval(timeUpdateInterval.value);
  if (httpPollingInterval.value) clearInterval(httpPollingInterval.value); // Clear http polling interval if it was set
  disconnect(); // Disconnect WebSocket
});
</script>

<style scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 0.5rem; /* Match main content area's rounded corners */
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.service-card {
  border: 1px solid #e5e7eb;
}

.service-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-running {
  background-color: #10b981;
}

.status-stopped {
  background-color: #ef4444;
}

.status-failed {
  background-color: #f59e0b;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

@media (min-width: 1024px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .service-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
