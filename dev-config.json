{"ServerPort": 8080, "VuePort": 3000, "Delay": 5, "LogLevel": "Verbose", "Services": {"GoServer": {"Name": "Go Server", "Command": "go", "Arguments": "run main.go -s", "Required": true, "HealthCheckPath": "/api/health"}, "GoClient": {"Name": "Go Client", "Command": "go", "Arguments": "run main.go -c", "Required": false}, "VueServer": {"Name": "Vue Development Server", "Command": "pnpm", "Arguments": "dev", "Required": false, "HealthCheckPath": "/"}}}