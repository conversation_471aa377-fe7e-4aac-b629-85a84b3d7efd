package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"./hertz_project/pkg/model"
	"github.com/gorilla/websocket" // Still needed for client-side WebSocket connections if not replaced by Fiber's client
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"
	proc "github.com/shirou/gopsutil/v4/process" // Alias for process package

	"golang.org/x/crypto/pbkdf2"
)

var (
	serverStatusRepo *dal.ServerStatusRepository
	serverInfoRepo   *dal.ServerInfoRepository
	serviceRepo      *dal.ServiceRepository
)

// [Translated] 用于企业级监控的网络速度跟踪
type NetworkSpeedTracker struct {
	LastNetIn  uint64
	LastNetOut uint64
	LastTime   time.Time
	mutex      sync.RWMutex
}

var networkTracker = &NetworkSpeedTracker{
	LastTime: time.Now(),
}

// [Translated] calculateNetworkSpeed 计算每秒字节数的网络速度
func (nt *NetworkSpeedTracker) calculateNetworkSpeed(currentNetIn, currentNetOut uint64) (uint64, uint64) {
	nt.mutex.Lock()
	defer nt.mutex.Unlock()

	now := time.Now()
	timeDiff := now.Sub(nt.LastTime).Seconds()

	var inSpeed, outSpeed uint64

	// [Translated] 网络速度计算的调试日志
	// log.Printf("Network Speed Debug - Current: In=%d, Out=%d, TimeDiff=%.2fs", currentNetIn, currentNetOut, timeDiff)
	// log.Printf("Network Speed Debug - Previous: In=%d, Out=%d", nt.LastNetIn, nt.LastNetOut)

	// [Translated] 仅在我们有先前数据和合理时间差的情况下计算速度
	// [Translated] 仅在首次运行（当LastTime为初始值时）或时间差过大时跳过计算
	isFirstRun := nt.LastNetIn == 0 && nt.LastNetOut == 0
	if timeDiff > 0 && timeDiff < 300 && !isFirstRun {
		// [Translated] 计算每秒字节数
		if currentNetIn >= nt.LastNetIn {
			inSpeed = uint64(float64(currentNetIn-nt.LastNetIn) / timeDiff)
		}
		if currentNetOut >= nt.LastNetOut {
			outSpeed = uint64(float64(currentNetOut-nt.LastNetOut) / timeDiff)
		}
		// log.Printf("网络速度调试 - 计算结果: 入站速度=%d B/s, 出站速度=%d B/s", inSpeed, outSpeed)
	} else {
		if isFirstRun {
			// log.Printf("网络速度调试 - 首次运行，初始化基线数据")
		} else {
			// log.Printf("网络速度调试 - 跳过计算: 时间差=%.2f, 上次入站=%d, 上次出站=%d", timeDiff, nt.LastNetIn, nt.LastNetOut)
		}
	}

	// [Translated] 更新跟踪数据
	nt.LastNetIn = currentNetIn
	nt.LastNetOut = currentNetOut
	nt.LastTime = now

	return inSpeed, outSpeed
}

type ServerConfig struct {
	Listen        string `json:"listen"`
	Port          string `json:"port"`
	LoginUsername string `json:"login_username"`
	LoginPassword string `json:"login_password"`
	Servers       []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	SecureCookie bool   `json:"secure_cookie"` // New field for cookie security
	Password     string `json:"password"`
}

type ClientConfig struct {
	MID       int    `json:"mid"`
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password   string `json:"password"`
	ServerInfo struct {
		Name         string `json:"name"`
		Tag          string `json:"tag"`
		IPv4         string `json:"ipv4"`
		IPv6         string `json:"ipv6"`
		AutoDetectIP bool   `json:"auto_detect_ip"`
	} `json:"server_info"`
}

type FrontendServerDetails struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	IP          string  `json:"ip"`
	Hostname    string  `json:"hostname"`
	OS          string  `json:"os"`
	Status      string  `json:"status"`
	CPU         float64 `json:"cpu"`
	Memory      float64 `json:"memory"`
	NetInSpeed  uint64  `json:"NetInSpeed"`
	NetOutSpeed uint64  `json:"NetOutSpeed"`
	Uptime      string  `json:"uptime"`
}

// calculateServerStatus 根据CPU和内存使用率计算服务器状态
func calculateServerStatus(cpuUsage, memoryUsage float64) string {
	if cpuUsage > 90 || memoryUsage > 90 {
		return "critical"
	} else if cpuUsage > 70 || memoryUsage > 70 {
		return "warning"
	} else {
		return "normal"
	}
}

// convertToServerDetailsForFrontend 将后端ServerDetails转换为前端期望的FrontendServerDetails
func convertToServerDetailsForFrontend(server model.ServerDetails, host model.ServerInfo, status model.StatusInfo) FrontendServerDetails {
	frontendDetails := FrontendServerDetails{
		ID:          server.ID,
		Name:        server.Name,
		IP:          server.IPv4,
		OS:          host.HostPlatform,
		CPU:         status.CPU,
		NetInSpeed:  status.NetInSpeed,
		NetOutSpeed: status.NetOutSpeed,
	}

	// Hostname 可以使用 Platform 或 PlatformVersion，或更复杂的逻辑
	frontendDetails.Hostname = host.HostPlatform

	// 计算 Memory 使用率
	if status.MemTotal > 0 {
		frontendDetails.Memory = (float64(status.MemUsed) / float64(status.MemTotal)) * 100
	}

	// 格式化 Uptime
	frontendDetails.Uptime = formatUptime(int64(status.Uptime))

	// 计算 Status
	frontendDetails.Status = calculateServerStatus(status.CPU, frontendDetails.Memory)

	return frontendDetails
}

// [Translated] WebSocket消息结构（用于客户端连接）
type Message struct {
	Type          string              `json:"type"`
	Password      string              `json:"password"`
	Data          model.ServerDetails `json:"data,omitempty"`
	EncryptedData string              `json:"encrypted_data,omitempty"`
	Encrypted     bool                `json:"encrypted"`
	Timestamp     time.Time           `json:"timestamp"`
	// New fields for service data
	SupervisorServices []model.SupervisorService `json:"supervisor_services,omitempty"`
	SystemdServices    []model.SystemdService    `json:"systemd_services,omitempty"`
	DockerServices     []model.DockerService     `json:"docker_services,omitempty"`
}

// [Translated] 前端WebSocket消息结构
type FrontendWSMessage struct {
	Type      string      `json:"type"`
	Token     string      `json:"token,omitempty"`
	ServerID  int         `json:"server_id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// [Translated] 前端客户端的WebSocket连接信息
type FrontendConnection struct {
	Conn         *websocket.Conn
	UserID       string
	SubscribedTo map[int]bool // [Translated] 此连接订阅的服务器ID
	LastPing     time.Time
}

// [Translated] 前端WebSocket连接的连接管理器
type ConnectionManager struct {
	connections map[string]*FrontendConnection
	mutex       sync.RWMutex
}

// [Translated] ServiceRequest表示服务操作请求
type ServiceRequest struct {
	ServerID    int    `json:"serverId"`
	ServiceName string `json:"serviceName"`
	ServiceType string `json:"serviceType"`
}

// [Translated] Service表示服务信息

var (
	isClient           = flag.Bool("c", false, "Run as client")
	isServer           = flag.Bool("s", false, "Run as server")
	configFile         = flag.String("f", "", "Configuration file path")
	ServerConfigGlobal *ServerConfig // Exported for access by other packages
	upgrader           = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // [Translated] 为简化起见，允许所有来源
		},
	}
	frontendConnManager = &ConnectionManager{
		connections: make(map[string]*FrontendConnection),
	}
)

// [Translated] 内存缓存用于存储最新的服务器状态
var serverStatusCache = struct {
	sync.RWMutex
	data map[int]*model.StatusInfo
}{
	data: make(map[int]*model.StatusInfo),
}

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "server.json"
		} else {
			*configFile = "client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

func runServer() {
	fmt.Println("Starting server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// [Translated] 全局存储配置以供处理程序使用
	ServerConfigGlobal = config

	// [Translated] 初始化数据库
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "server.db"
	}
	dal.InitDatabase(dbPath)
	// Initialize repositories
	serverStatusRepo = dal.NewServerStatusRepository(dal.GetDB())
	serverInfoRepo = dal.NewServerInfoRepository(dal.GetDB())
	serviceRepo = dal.NewServiceRepository(dal.GetDB())

	// [Translated] 初始化认证处理程序
	// authHandler := auth.NewAuthHandler(config.LoginUsername, config.LoginPassword, config.SecureCookie) // authHandler will be used with http.HandleFunc

	// [Translated] 设置认证端点 (using standard http for now)
	http.HandleFunc("/api/login", auth.HandleLogin)          // Assuming auth.HandleLogin is adapted for http.ResponseWriter, *http.Request
	http.HandleFunc("/api/refresh", auth.HandleRefreshToken) // Assuming auth.HandleRefreshToken is adapted for http.ResponseWriter, *http.Request

	// [Translated] 设置受保护的API端点 (using standard http for now)
	http.HandleFunc("/api/servers", authMiddleware(getServersAPI))
	http.HandleFunc("/api/system/stats", authMiddleware(getSystemStatsAPI))
	http.HandleFunc("/api/services/list", authMiddleware(handleListServicesAPI))
	http.HandleFunc("/api/services/supervisor/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/supervisor/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/supervisor/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/logs", authMiddleware(handleGetServiceLogsAPI))

	// [Translated] 设置客户端连接的WebSocket处理程序 (using standard http for now)
	http.HandleFunc(config.WebSocket.Path, func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(w, r, config.Password)
	})

	// [Translated] 设置前端连接的WebSocket处理程序 (using standard http for now)
	http.HandleFunc("/ws/frontend", handleFrontendWebSocket)

	// [Translated] 设置静态文件服务 (using standard http for now)
	http.HandleFunc("/", serveIndex)

	// [Translated] 启动实时数据广播
	go startRealTimeDataBroadcast()

	// [Translated] 启动HTTP服务器
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	fmt.Printf("Server listening on %s%s\n", addr, config.WebSocket.Path)
	fmt.Printf("Frontend WebSocket available at ws://%s/ws/frontend\n", addr)
	fmt.Printf("Web interface available at http://%s/\n", addr)
	fmt.Printf("Login endpoint available at http://%s/api/login\n", addr)
	fmt.Printf("API endpoint available at http://%s/api/servers\n", addr)

	// [Translated] 启动标准的net/http服务器
	log.Fatal(http.ListenAndServe(addr, nil))
}

func runClient() {
	// fmt.Println("Starting client mode...") // Commented out to reduce client output

	config, err := loadClientConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load client config: %v", err)
	}

	// [Translated] 初始化本地数据库
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "client.db"
	}
	// DAL initialization is only for server mode.
	// For client mode, we will handle direct GORM usage or future DAL integration separately.

	// [Translated] 连接到服务器并开始监控
	connectAndMonitor(config)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	fmt.Printf("Client connected: %s\n", r.RemoteAddr)

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		// [Translated] 验证密码
		if msg.Password != password {
			log.Printf("Invalid password from %s", r.RemoteAddr)
			continue
		}

		var serverDetails model.ServerDetails

		if msg.Encrypted && msg.EncryptedData != "" {
			// [Translated] 解密数据
			decryptedData, err := decrypt(msg.EncryptedData, password)
			if err != nil {
				log.Printf("Failed to decrypt data from %s: %v", r.RemoteAddr, err)
				continue
			}

			// [Translated] 解密数据
			err = json.Unmarshal(decryptedData, &serverDetails)
			if err != nil {
				log.Printf("Failed to unmarshal decrypted data from %s: %v", r.RemoteAddr, err)
				continue
			}
		} else {
			// [Translated] 使用未加密数据（向后兼容）
			serverDetails = msg.Data
		}

		// [Translated] 将数据存储到数据库
		serverDetails.LastActive = time.Now().Unix()

		// [Translated] 创建或更新 ServerStatus
		serverStatus := model.ServerStatus{
			ID:                serverDetails.ID,
			Name:              serverDetails.Name,
			Tag:               serverDetails.Tag,
			LastActive:        serverDetails.LastActive,
			IPv4:              serverDetails.IPv4,
			IPv6:              serverDetails.IPv6,
			ValidIP:           serverDetails.ValidIP,
			StatusCPU:         serverDetails.Status.CPU,
			StatusMemUsed:     serverDetails.Status.MemUsed,
			StatusMemTotal:    serverDetails.Status.MemTotal,
			StatusDiskUsed:    serverDetails.Status.DiskUsed,
			StatusDiskTotal:   serverDetails.Status.DiskTotal,
			StatusNetInSpeed:  serverDetails.Status.NetInSpeed,
			StatusNetOutSpeed: serverDetails.Status.NetOutSpeed,
			StatusUptime:      serverDetails.Status.Uptime,
			StatusLoad1:       serverDetails.Status.Load1,
			StatusLoad5:       serverDetails.Status.Load5,
			StatusLoad15:      serverDetails.Status.Load15,
		}
		if err := serverStatusRepo.SaveServerStatus(&serverStatus); err != nil {
			log.Printf("Database error (ServerStatus) after retries: %v", err)
		}

		// [Translated] 更新内存缓存
		serverStatusCache.Lock()
		serverStatusCache.data[serverStatus.ID] = &model.StatusInfo{
			CPU:         serverStatus.StatusCPU,
			MemUsed:     serverStatus.StatusMemUsed,
			MemTotal:    serverStatus.StatusMemTotal,
			DiskUsed:    serverStatus.StatusDiskUsed,
			DiskTotal:   serverStatus.StatusDiskTotal,
			NetInSpeed:  serverStatus.StatusNetInSpeed,
			NetOutSpeed: serverStatus.StatusNetOutSpeed,
			Uptime:      serverStatus.StatusUptime,
			Load1:       serverStatus.StatusLoad1,
			Load5:       serverStatus.StatusLoad5,
			Load15:      serverStatus.StatusLoad15,
		}
		serverStatusCache.Unlock()

		// [Translated] 创建或更新 ServerInfo
		serverInfo := model.ServerInfo{
			ID:                  serverDetails.ID,
			HostPlatform:        serverDetails.Host.Platform,
			HostPlatformVersion: serverDetails.Host.PlatformVersion,
			HostCPU:             serverDetails.Host.CPU,
			HostArch:            serverDetails.Host.Arch,
			HostVirtualization:  serverDetails.Host.Virtualization,
			HostBootTime:        serverDetails.Host.BootTime,
			HostCountryCode:     serverDetails.Host.CountryCode,
			HostVersion:         serverDetails.Host.Version,
		}
		if err := serverInfoRepo.UpdateServerInfo(&serverInfo); err != nil {
			log.Printf("Database error (ServerInfo) after retries: %v", err)
		}

		// [Translated] 保存历史监控数据
		serverStatusHistory := model.ServerStatusHistory{
			ServerID:          serverStatus.ID,
			Timestamp:         time.Now(),
			StatusCPU:         serverStatus.StatusCPU,
			StatusMemUsed:     serverStatus.StatusMemUsed,
			StatusMemTotal:    serverStatus.StatusMemTotal,
			StatusDiskUsed:    serverStatus.StatusDiskUsed,
			StatusDiskTotal:   serverStatus.StatusDiskTotal,
			StatusNetInSpeed:  serverStatus.StatusNetInSpeed,
			StatusNetOutSpeed: serverStatus.StatusNetOutSpeed,
			StatusUptime:      serverStatus.StatusUptime,
			StatusLoad1:       serverStatus.StatusLoad1,
			StatusLoad5:       serverStatus.StatusLoad5,
			StatusLoad15:      serverStatus.StatusLoad15,
		}
		if err := serverStatusRepo.CreateServerStatusHistory(&serverStatusHistory); err != nil {
			log.Printf("Database error (ServerStatusHistory) after retries: %v", err)
		}

		// [Translated] 异步处理服务数据
		go serviceRepo.ProcessServiceUpdates(serverDetails.ID, msg.SupervisorServices, msg.SystemdServices, msg.DockerServices)
	}

	fmt.Printf("Client disconnected: %s\n", r.RemoteAddr)
}

// [Translated] 获取所有服务器数据的API处理程序
func getServersAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	var responseServers []FrontendServerDetails

	// Collect all server IDs from the config
	var serverIDs []int
	for _, s := range ServerConfigGlobal.Servers {
		serverIDs = append(serverIDs, s.ID)
	}

	// Fetch all ServerStatus records in a single query
	allServerStatuses, err := serverStatusRepo.GetAllServerStatuses(serverIDs)
	if err != nil {
		log.Printf("Database error fetching all ServerStatus: %v", err)
		http.Error(w, "Failed to retrieve server statuses.", http.StatusInternalServerError)
		return
	}

	// Create a map for quick lookup
	serverStatusMap := make(map[int]model.ServerStatus)
	for _, ss := range allServerStatuses {
		serverStatusMap[ss.ID] = ss
	}

	// Fetch all ServerInfo records in a single query
	allServerInfos, err := serverInfoRepo.GetAllServerInfos(serverIDs)
	if err != nil {
		log.Printf("Database error fetching all ServerInfo: %v", err)
		http.Error(w, "Failed to retrieve server information.", http.StatusInternalServerError)
		return
	}

	// Create a map for quick lookup
	serverInfoMap := make(map[int]model.ServerInfo)
	for _, si := range allServerInfos {
		serverInfoMap[si.ID] = si
	}

	// Process configured servers using the fetched data
	for _, s := range ServerConfigGlobal.Servers {
		serverStatus, statusFound := serverStatusMap[s.ID]
		serverInfo, infoFound := serverInfoMap[s.ID]

		if !statusFound || !infoFound {
			// If either status or info is not found, consider the server offline or unknown
			status := "offline"
			if !statusFound && infoFound { // If info is found but status isn't, it might be a new server or not reporting stats yet
				status = "no data"
			} else if statusFound && !infoFound { // If status is found but info isn't (shouldn't happen with correct data, but for robustness)
				status = "unknown info"
			}

			responseServers = append(responseServers, FrontendServerDetails{
				ID:          s.ID,
				Name:        s.Name,
				IP:          "",
				Hostname:    "",
				OS:          "",
				Status:      status,
				CPU:         0,
				Memory:      0,
				NetInSpeed:  0,
				NetOutSpeed: 0,
				Uptime:      "0 seconds",
			})
			log.Printf("Server ID %d (%s) missing data (status found: %t, info found: %t), returning %s status.", s.ID, s.Name, statusFound, infoFound, status)
			continue
		}

		// Combine data and convert to frontend expected structure
		serverDetail := convertToServerDetailsForFrontend(
			model.ServerDetails{
				ID:         serverStatus.ID,
				Name:       serverStatus.Name,
				Tag:        serverStatus.Tag,
				LastActive: serverStatus.LastActive,
				IPv4:       serverStatus.IPv4,
				IPv6:       serverStatus.IPv6,
				ValidIP:    serverStatus.ValidIP,
			},
			serverInfo, // Pass serverInfo (HostInfo)
			model.StatusInfo{ // Pass StatusInfo
				CPU:         serverStatus.StatusCPU,
				MemUsed:     serverStatus.StatusMemUsed,
				MemTotal:    serverStatus.StatusMemTotal,
				DiskUsed:    serverStatus.StatusDiskUsed,
				DiskTotal:   serverStatus.StatusDiskTotal,
				NetInSpeed:  serverStatus.StatusNetInSpeed,
				NetOutSpeed: serverStatus.StatusNetOutSpeed,
				Uptime:      serverStatus.StatusUptime,
				Load1:       serverStatus.StatusLoad1,
				Load5:       serverStatus.StatusLoad5,
				Load15:      serverStatus.StatusLoad15,
			},
		)
		responseServers = append(responseServers, serverDetail)
	}

	json.NewEncoder(w).Encode(responseServers)
}

// [Translated] 获取实时系统统计信息的API处理程序
func getSystemStatsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// [Translated] 检查查询参数中是否提供了serverID
	serverIDStr := r.URL.Query().Get("serverId")
	if serverIDStr != "" {
		// [Translated] 从数据库获取客户端统计信息
		serverID, err := strconv.Atoi(serverIDStr)
		if err != nil {
			log.Printf("Invalid serverID parameter: %v", err)
			http.Error(w, "Invalid serverID parameter.", http.StatusBadRequest)
			return
		}

		stats, found := getLatestClientStats(serverID)
		if !found {
			log.Printf("Failed to get client stats for server %d", serverID) // Removed %v, err as it's not always an error from getLatestClientStats
			http.Error(w, fmt.Sprintf("Failed to get statistics for server ID %d. Server may be offline or data not yet available.", serverID), http.StatusInternalServerError)
			return
		}

		json.NewEncoder(w).Encode(stats)
		return
	}

	// [Translated] 回退：获取服务器自己的系统统计信息
	stats, err := getCurrentSystemStats()
	if err != nil {
		log.Printf("Failed to get system stats: %v", err)
		http.Error(w, "Failed to get current system statistics.", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(stats)
}

// [Translated] getCurrentSystemStats获取当前系统统计信息
func getCurrentSystemStats() (*model.StatusInfo, error) {
	// [Translated] 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// [Translated] 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("Error getting memory info in getCurrentSystemStats: %v", err)
		return nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	log.Printf("Debug: getCurrentSystemStats - MemTotal: %d", memInfo.Total)

	// [Translated] 获取磁盘信息
	diskInfo, err := disk.Usage("/")
	if err != nil {
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("failed to get disk info: %v", err)
		}
	}

	// [Translated] 获取平均负载
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// [Translated] 获取网络I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// [Translated] 使用企业级跟踪计算网络速度
	netInSpeed, netOutSpeed := networkTracker.calculateNetworkSpeed(netIn, netOut)

	// [Translated] 获取交换信息
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// [Translated] 获取主机正常运行时间信息
	hostInfo, err := host.Info()
	var uptime uint64
	if err == nil {
		uptime = uint64(time.Now().Unix() - int64(hostInfo.BootTime))
	}

	// [Translated] 创建状态信息
	stats := &model.StatusInfo{
		CPU:            cpuUsage,
		MemUsed:        memInfo.Used,
		MemTotal:       memInfo.Total,
		SwapUsed:       swapUsed,
		DiskUsed:       diskInfo.Used,
		DiskTotal:      diskInfo.Total,
		NetInTransfer:  netIn,
		NetOutTransfer: netOut,
		NetInSpeed:     netInSpeed,  // [Translated] 实时下载速度
		NetOutSpeed:    netOutSpeed, // [Translated] 实时上传速度
		Uptime:         uptime,
		Load1:          load1,
		Load5:          load5,
		Load15:         load15,
		TcpConnCount:   0, // [Translated] 需要额外实现
		UdpConnCount:   0, // [Translated] 需要额外实现
		ProcessCount:   int(hostInfo.Procs),
	}

	return stats, nil
}

// [Translated] 提供静态文件
func serveIndex(w http.ResponseWriter, r *http.Request) {
	// [Translated] 设置CSP安全头，防止XSS攻击
	cspHeader := "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tailwindcss.com unpkg.com; style-src 'self' 'unsafe-inline' unpkg.com; connect-src 'self' ws: wss:;"
	w.Header().Set("Content-Security-Policy", cspHeader)

	// [Translated] 处理登录页面
	if r.URL.Path == "/login" || r.URL.Path == "/login.html" {
		http.ServeFile(w, r, getFilePath("web/login.html"))
		return
	}

	// [Translated] 处理根路径和登录页面
	if r.URL.Path == "/" || r.URL.Path == "/login" || r.URL.Path == "/login.html" {
		http.ServeFile(w, r, getFilePath("web/login.html"))
		return
	}

	// [Translated] 处理仪表板（登录后的主页）
	if r.URL.Path == "/dashboard" || r.URL.Path == "/index.html" {
		http.ServeFile(w, r, getFilePath("web/index.html"))
		return
	}

	// [Translated] 对于其他路径，尝试直接提供文件
	filePath := r.URL.Path[1:] // [Translated] 移除前导斜杠
	http.ServeFile(w, r, getFilePath("web/"+filePath))
}

// [Translated] execCommand执行shell命令并返回其输出或错误。
func handleListServicesAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	serviceType := r.URL.Query().Get("serviceType")
	serverIDStr := r.URL.Query().Get("serverId")

	log.Printf("List services request: serviceType=%s, serverId=%s", serviceType, serverIDStr)

	if serviceType == "" {
		log.Printf("Missing serviceType parameter")
		http.Error(w, "serviceType parameter is required.", http.StatusBadRequest)
		return
	}

	serverID, err := strconv.Atoi(serverIDStr)
	if err != nil {
		log.Printf("Invalid serverID parameter: %v", err)
		http.Error(w, "Invalid serverID parameter.", http.StatusBadRequest)
		return
	}

	var services interface{}
	switch serviceType {
	case "supervisor":
		supervisorServices, err := serviceRepo.GetSupervisorServicesByServerID(serverID)
		if err != nil {
			log.Printf("Failed to get Supervisor services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to retrieve Supervisor services.", http.StatusInternalServerError)
			return
		}
		services = supervisorServices
	case "systemd":
		systemdServices, err := serviceRepo.GetSystemdServicesByServerID(serverID)
		if err != nil {
			log.Printf("Failed to get Systemd services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to retrieve Systemd services.", http.StatusInternalServerError)
			return
		}
		services = systemdServices
	case "docker":
		dockerServices, err := serviceRepo.GetDockerServicesByServerID(serverID)
		if err != nil {
			log.Printf("Failed to get Docker services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to retrieve Docker services.", http.StatusInternalServerError)
			return
		}
		services = dockerServices
	default:
		log.Printf("Invalid service type: %s", serviceType)
		http.Error(w, fmt.Sprintf("Invalid service type: %s", serviceType), http.StatusBadRequest)
		return
	}

	log.Printf("Successfully listed services for ServerID %d", serverID)
	json.NewEncoder(w).Encode(services)
}

func handleControlServiceAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// [Translated] 处理预检OPTIONS请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	log.Printf("Service control request: method=%s, path=%s, content-type=%s",
		r.Method, r.URL.Path, r.Header.Get("Content-Type"))

	if r.Method != http.MethodPost {
		log.Printf("Invalid method for service control: %s", r.Method)
		http.Error(w, fmt.Sprintf("Only POST method is allowed, got %s", r.Method), http.StatusMethodNotAllowed)
		return
	}

	// [Translated] 读取原始请求体以进行日志记录
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body.", http.StatusBadRequest)
		return
	}

	log.Printf("Raw request body: %s", string(bodyBytes))

	// [Translated] 解析JSON
	var req ServiceRequest
	err = json.Unmarshal(bodyBytes, &req)
	if err != nil {
		log.Printf("Failed to decode request body: %v", err)
		http.Error(w, fmt.Sprintf("Invalid JSON format: %v", err), http.StatusBadRequest)
		return
	}

	// [Translated] 从URL路径中提取操作
	action := strings.TrimPrefix(r.URL.Path, "/api/services/"+req.ServiceType+"/")
	log.Printf("Parsed request: type=%s, name=%s, action=%s, serverID=%d",
		req.ServiceType, req.ServiceName, action, req.ServerID)

	// [Translated] 验证必填字段
	if req.ServiceName == "" {
		log.Printf("Missing serviceName field")
		http.Error(w, "serviceName is required.", http.StatusBadRequest)
		return
	}

	if req.ServiceType == "" {
		log.Printf("Missing serviceType field")
		http.Error(w, "serviceType is required.", http.StatusBadRequest)
		return
	}

	if action == "" {
		log.Printf("Missing action in URL path")
		http.Error(w, "action is required in URL path.", http.StatusBadRequest)
		return
	}

	// [Translated] 执行服务控制
	err = controlService(req.ServiceType, req.ServiceName, action)
	if err != nil {
		log.Printf("Service control failed: %v", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("Service control successful: %s %s %s", req.ServiceType, action, req.ServiceName)
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Service operation successful"})
}

func handleGetServiceLogsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain") // [Translated] 日志是纯文本
	w.Header().Set("Access-Control-Allow-Origin", "*")

	serviceType := r.URL.Query().Get("serviceType")
	serviceName := r.URL.Query().Get("serviceName")

	if serviceType == "" || serviceName == "" {
		http.Error(w, "serviceType and serviceName parameters are required.", http.StatusBadRequest)
		return
	}

	logs, err := getServiceLogs(serviceType, serviceName)
	if err != nil {
		// getServiceLogs already returns user-friendly error messages for common issues.
		// We will just wrap it in a generic internal server error for the API response.
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Write([]byte(logs))
}

func connectAndMonitor(config *ClientConfig) {
	url := fmt.Sprintf("ws://%s:%s%s", config.Server, config.Port, config.WebSocket.Path)

	for {
		// fmt.Printf("Connecting to %s...\n", url) // Commented out to reduce client output

		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Connection failed: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		fmt.Println("Connected to server")

		// [Translated] 启动监控循环
		monitorLoop(conn, config.Password, config)

		conn.Close()
		// fmt.Println("Disconnected, retrying in 5 seconds...") // Commented out to reduce client output
		time.Sleep(5 * time.Second)
	}
}

func monitorLoop(conn *websocket.Conn, password string, config *ClientConfig) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		serverDetails, supervisorServices, systemdServices, dockerServices, err := collectServerDetails(config)
		if err != nil {
			log.Printf("Failed to collect server details: %v", err)
			continue
		}

		// [Translated] 本地存储（创建不带固定ID的副本以进行本地存储）
		// [Translated] 注意：随着表的拆分，本地存储逻辑可能需要调整或删除
		// [Translated] 当前，我们将跳过本地存储以简化操作
		// localDetails := *serverDetails
		// localDetails.ID = 0 // [Translated] 让数据库自动生成本地存储的ID
		// if err := saveWithRetry(&localDetails, 3); err != nil {
		// 	log.Printf("Failed to save local details after retries: %v", err)
		// }

		// [Translated] 加密服务器详细信息
		serverDetailsJSON, err := json.Marshal(serverDetails)
		if err != nil {
			log.Printf("Failed to marshal server details: %v", err)
			continue
		}

		encryptedData, err := encrypt(serverDetailsJSON, password)
		if err != nil {
			log.Printf("Failed to encrypt data: %v", err)
			continue
		}

		// [Translated] 发送到服务器
		msg := Message{
			Type:               "system_info",
			Password:           password,
			EncryptedData:      encryptedData,
			Encrypted:          true,
			Timestamp:          time.Now(),
			SupervisorServices: supervisorServices,
			SystemdServices:    systemdServices,
			DockerServices:     dockerServices,
		}

		err = conn.WriteJSON(msg)
		if err != nil {
			log.Printf("Send error: %v", err)
			return
		}

		// fmt.Printf("Sent Server[%d:%s]: CPU=%.2f%%, Mem=%.2fGB\n",
		// 	serverDetails.ID, serverDetails.Name, serverDetails.Status.CPU,
		// 	float64(serverDetails.Status.MemUsed)/1024/1024/1024)
	}
}

func collectServerDetails(config *ClientConfig) (*model.ServerDetails, []model.SupervisorService, []model.SystemdService, []model.DockerService, error) {
	serverDetails := &model.ServerDetails{
		ID:   config.MID, // [Translated] 使用配置中的MID进行服务器识别
		Name: config.ServerInfo.Name,
		Tag:  config.ServerInfo.Tag,
	}

	var supervisorServices []model.SupervisorService
	var systemdServices []model.SystemdService
	var dockerServices []model.DockerService

	// [Translated] 如果启用，自动检测IP
	if config.ServerInfo.AutoDetectIP {
		// [Translated] 这将通过实际的IP检测逻辑实现
		serverDetails.IPv4 = "127.0.0.1" // [Translated] 占位符
		serverDetails.ValidIP = serverDetails.IPv4
	} else {
		serverDetails.IPv4 = config.ServerInfo.IPv4
		serverDetails.IPv6 = config.ServerInfo.IPv6
		if serverDetails.IPv4 != "" {
			serverDetails.ValidIP = serverDetails.IPv4
		} else {
			serverDetails.ValidIP = serverDetails.IPv6
		}
	}

	// [Translated] 获取主机信息
	hostInfo, err := host.Info()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get host info: %v", err)
	}
	// log.Printf("Operating System: %s, Platform: %s, PlatformFamily: %s, PlatformVersion: %s",
	// 	hostInfo.OS, hostInfo.Platform, hostInfo.PlatformFamily, hostInfo.PlatformVersion)

	// [Translated] 获取CPU信息
	cpuInfo, err := cpu.Info()
	var cpuNames []string
	if err == nil && len(cpuInfo) > 0 {
		cpuNames = []string{cpuInfo[0].ModelName}
	} else {
		cpuNames = []string{"Unknown CPU"}
	}

	// [Translated] 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("Error getting memory info in collectServerDetails: %v", err)
		return nil, nil, nil, nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	// log.Printf("Debug: collectServerDetails - MemTotal: %d", memInfo.Total)

	// [Translated] 获取磁盘信息
	// [Translated] 获取磁盘信息
	var diskTotal, diskUsed uint64
	if hostInfo.OS == "linux" {
		diskInfo, err := disk.Usage("/")
		if err != nil {
			log.Printf("Failed to get disk info for / on Linux: %v", err)
		} else {
			diskTotal = diskInfo.Total
			diskUsed = diskInfo.Used
		}
	} else if hostInfo.OS == "windows" {
		diskInfo, err := disk.Usage("C:")
		if err != nil {
			log.Printf("Failed to get disk info for C: on Windows: %v", err)
		} else {
			diskTotal = diskInfo.Total
			diskUsed = diskInfo.Used
		}
	} else {
		// log.Printf("Disk usage collection skipped for non-Linux/Windows OS: %s", hostInfo.OS)
	}
	// log.Printf("Debug: collectServerDetails - DiskTotal: %d, DiskUsed: %d", diskTotal, diskUsed)

	// [Translated] 将CPU名称转换为JSON字符串
	cpuJSON, _ := json.Marshal(cpuNames)

	// [Translated] 填充主机信息
	serverDetails.Host = model.HostInfo{
		Platform:        hostInfo.Platform,
		PlatformVersion: hostInfo.PlatformVersion,
		CPU:             string(cpuJSON),
		MemTotal:        memInfo.Total,
		DiskTotal:       diskTotal,
		SwapTotal:       memInfo.SwapTotal,
		Arch:            hostInfo.KernelArch,
		Virtualization:  hostInfo.VirtualizationSystem,
		BootTime:        int64(hostInfo.BootTime),
		CountryCode:     "auto", // [Translated] 将在实际实现中检测到
		Version:         hostInfo.KernelVersion,
	}

	// [Translated] 获取当前状态
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// [Translated] 获取平均负载
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// [Translated] 获取网络I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// [Translated] 使用企业级跟踪计算网络速度
	netInSpeed, netOutSpeed := networkTracker.calculateNetworkSpeed(netIn, netOut)

	// [Translated] 获取交换信息
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// [Translated] 填充状态信息
	serverDetails.Status = model.StatusInfo{
		CPU:            cpuUsage,
		MemUsed:        memInfo.Used,
		MemTotal:       memInfo.Total, // Added MemTotal
		SwapUsed:       swapUsed,
		DiskUsed:       diskUsed,
		DiskTotal:      diskTotal, // Add DiskTotal to StatusInfo
		NetInTransfer:  netIn,
		NetOutTransfer: netOut,
		NetInSpeed:     netInSpeed,  // [Translated] 实时下载速度
		NetOutSpeed:    netOutSpeed, // [Translated] 实时上传速度
		Uptime:         uint64(time.Now().Unix() - int64(hostInfo.BootTime)),
		Load1:          load1,
		Load5:          load5,
		Load15:         load15,
		TcpConnCount:   0, // [Translated] 需要额外实现
		UdpConnCount:   0, // [Translated] 需要额外实现
		ProcessCount:   int(hostInfo.Procs),
	}

	// [Translated] 收集Supervisor服务
	supServices, err := listSupervisorServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Supervisor services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		supervisorServices = supServices
	}

	// [Translated] 收集Systemd服务
	sysdServices, err := listSystemdServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Systemd services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		systemdServices = sysdServices
	}

	// [Translated] 收集Docker服务
	docServices, err := listDockerServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Docker services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		dockerServices = docServices
	}

	return serverDetails, supervisorServices, systemdServices, dockerServices, nil
}

// [Translated] 加密函数
const (
	keySize    = 16    // [Translated] AES-128
	nonceSize  = 12    // [Translated] GCM nonce大小
	saltSize   = 16    // [Translated] PBKDF2盐大小
	iterations = 10000 // [Translated] PBKDF2迭代次数
)

// [Translated] deriveKey使用PBKDF2从密码派生密钥
func deriveKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)
}

// [Translated] encrypt使用AES-128-GCM加密数据
func encrypt(data []byte, password string) (string, error) {
	// [Translated] 生成随机盐
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %v", err)
	}

	// [Translated] 从密码派生密钥
	key := deriveKey(password, salt)

	// [Translated] 创建AES密码
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// [Translated] 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// [Translated] 生成随机nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// [Translated] 加密数据
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// [Translated] 组合盐 + nonce + 密文
	result := make([]byte, saltSize+nonceSize+len(ciphertext))
	copy(result[:saltSize], salt)
	copy(result[saltSize:saltSize+nonceSize], nonce)
	copy(result[saltSize+nonceSize:], ciphertext)

	// [Translated] 返回base64编码结果
	return base64.StdEncoding.EncodeToString(result), nil
}

// [Translated] decrypt使用AES-128-GCM解密数据
func decrypt(encryptedData string, password string) ([]byte, error) {
	// [Translated] 解码base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// [Translated] 检查最小长度
	if len(data) < saltSize+nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// [Translated] 提取盐、nonce和密文
	salt := data[:saltSize]
	nonce := data[saltSize : saltSize+nonceSize]
	ciphertext := data[saltSize+nonceSize:]

	// [Translated] 从密码派生密钥
	key := deriveKey(password, salt)

	// [Translated] 创建AES密码
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// [Translated] 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// [Translated] 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	return plaintext, nil
}

// [Translated] listServices列出指定类型的服务

// [Translated] controlService控制服务（启动、停止、重启）
func controlService(serviceType, serviceName, action string) error {
	switch serviceType {
	case "supervisor":
		return controlSupervisorService(serviceName, action)
	case "systemd":
		return controlSystemdService(serviceName, action)
	case "docker":
		return controlDockerService(serviceName, action)
	default:
		return fmt.Errorf("invalid service type: %s", serviceType)
	}
}

// [Translated] getServiceLogs获取服务的日志
func getServiceLogs(serviceType, serviceName string) (string, error) {
	switch serviceType {
	case "supervisor":
		return getSupervisorLogs(serviceName)
	case "systemd":
		return getSystemdLogs(serviceName)
	case "docker":
		return getDockerLogs(serviceName)
	default:
		return "", fmt.Errorf("invalid service type: %s", serviceType)
	}
}

// [Translated] executeCommand执行系统命令并返回输出
func executeCommand(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	output, err := cmd.CombinedOutput()
	return string(output), err
}

// [Translated] Supervisor服务的辅助函数
func listSupervisorServices() ([]model.SupervisorService, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockSupervisorServices(), nil
	}

	// log.Printf("执行 supervisorctl status 命令...")
	output, err := executeCommand("supervisorctl", "status")
	if err != nil {
		// log.Printf("supervisorctl status 失败: %v, 输出: %s", err, output) // 添加输出到日志

		// 检查是否是ExitError且退出代码为3（某些程序已停止）
		if exitErr, ok := err.(*exec.ExitError); ok && exitErr.ExitCode() == 3 {
			// log.Printf("supervisorctl 以状态3退出（某些程序已停止），继续解析输出。")
			// 继续解析输出，不返回模拟数据
		} else if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("supervisorctl not found. Please install supervisor: sudo apt install supervisor")
			}
			return getMockSupervisorServices(), nil
		} else {
			// 对于任何其他Supervisor错误，返回模拟数据以防止500错误
			// log.Printf("supervisorctl 错误，返回模拟数据用于测试: %v", err)
			return getMockSupervisorServices(), nil
		}
	}

	// log.Printf("supervisorctl status 输出: %s", output)

	var services []model.SupervisorService
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 2 {
			// log.Printf("跳过此行: 行中部分不足 (%d): %s", len(parts), line)
			continue
		}

		name := parts[0]
		status := parts[1]
		description := ""
		pidStr := ""
		uptimeStr := ""

		// [Translated] supervisorctl status output format can vary.
		// [Translated] Example: "program_name                 RUNNING   pid 1234, uptime 0:00:05"
		// [Translated] Or: "program_name                 FATAL     Exited too quickly"
		if len(parts) > 2 {
			// [Translated] Check for "pid" and "uptime" keywords
			pidIndex := -1
			uptimeIndex := -1
			for i, part := range parts {
				if part == "pid" {
					pidIndex = i
				}
				if part == "uptime" {
					uptimeIndex = i
				}
			}

			if pidIndex != -1 && pidIndex+1 < len(parts) {
				pidStr = strings.TrimSuffix(parts[pidIndex+1], ",") // Remove trailing comma
			}
			if uptimeIndex != -1 && uptimeIndex+1 < len(parts) {
				uptimeStr = strings.Join(parts[uptimeIndex+1:], " ")
			}

			// [Translated] If "pid" and "uptime" are present, description is before "pid"
			if pidIndex != -1 {
				description = strings.Join(parts[2:pidIndex], " ")
			} else {
				// [Translated] Otherwise, the rest of the line is description
				description = strings.Join(parts[2:], " ")
			}
		}

		service := model.SupervisorService{
			Name:        name,
			Status:      strings.ToLower(status),
			Description: description,
			PID:         pidStr,
			Uptime:      parseUptimeStringToSeconds(uptimeStr), // Convert string to int64 seconds
		}

		// [Translated] 获取进程的CPU和内存使用情况
		if service.PID != "" {
			pid, err := strconv.ParseInt(service.PID, 10, 32)
			if err == nil {
				p, err := proc.NewProcess(int32(pid)) // Use the alias 'proc'
				if err == nil {
					cpuPercent, err := p.CPUPercent()
					if err == nil {
						service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
					} else {
						log.Printf("Failed to get CPU for PID %d: %v", pid, err)
						service.CPU = "--" // Set to -- if failed
					}

					memInfo, err := p.MemoryInfo()
					if err == nil {
						service.Memory = formatBytes(memInfo.RSS)
					} else {
						log.Printf("Failed to get Memory for PID %d: %v", pid, err)
						service.Memory = "--" // Set to -- if failed
					}
				} else {
					log.Printf("Failed to create process for PID %d: %v", pid, err)
				}
			} else {
				log.Printf("Failed to parse PID '%s': %v", service.PID, err)
			}
		}

		// log.Printf("Parsed service: %+v", service)
		services = append(services, service)
	}

	// log.Printf("成功解析 %d 个 supervisor 服务", len(services))
	return services, nil
}

// [Translated] 格式化字节为人类可读的单位
func formatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := uint64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}

// 获取模拟的 Supervisor 服务数据（用于测试）
func getMockSupervisorServices() []model.SupervisorService {
	return []model.SupervisorService{
		{
			Name:        "xr-hertz:xr-hertz_00",
			Status:      "running",
			Description: "Hertz web service instance 00",
			PID:         "1234",
			Uptime:      parseUptimeStringToSeconds("2 days, 14:30:25"), // Convert string to int64 seconds
			Memory:      "128 MB",                                       // Added mock memory
			CPU:         "5.23%",                                        // Added mock CPU
		},
		{
			Name:        "xr-hertz:xr-hertz_01",
			Status:      "stopped",
			Description: "Hertz web service instance 01",
			PID:         "",
			Uptime:      0,    // Stopped services have 0 uptime
			Memory:      "--", // Added mock memory
			CPU:         "--", // Added mock CPU
		},
		{
			Name:        "nginx-proxy",
			Status:      "running",
			Description: "Nginx reverse proxy",
			PID:         "5678",
			Uptime:      parseUptimeStringToSeconds("5 days, 08:15:42"), // Convert string to int64 seconds
			Memory:      "64 MB",                                        // Added mock memory
			CPU:         "1.50%",                                        // Added mock CPU
		},
		{
			Name:        "redis-server",
			Status:      "running",
			Description: "Redis cache server",
			PID:         "9012",
			Uptime:      parseUptimeStringToSeconds("3 days, 12:45:18"), // Convert string to int64 seconds
			Memory:      "256 MB",                                       // Added mock memory
			CPU:         "3.10%",                                        // Added mock CPU
		},
		{
			Name:        "celery-worker",
			Status:      "failed",
			Description: "Celery background worker",
			PID:         "",
			Uptime:      0,    // Failed services have 0 uptime
			Memory:      "--", // Added mock memory
			CPU:         "--", // Added mock CPU
		},
	}
}

func controlSupervisorService(serviceName, action string) error {
	output, err := executeCommand("supervisorctl", action, serviceName)
	if err != nil {
		errorMessage := fmt.Sprintf("supervisorctl %s %s failed: %v, output: %s", action, serviceName, err, output)
		log.Printf("Error controlling Supervisor service: %s", errorMessage)

		// [Translated] 提供更具体的错误提示
		if strings.Contains(output, "ERROR (no such process)") || strings.Contains(output, "No such process") {
			errorMessage += "\n\nPossible reasons:\n- The service name might be incorrect. Please check the exact program name in your Supervisor configuration (e.g., /etc/supervisor/conf.d/).\n- The service is not configured or managed by Supervisor.\n- The service is not running and cannot be stopped/restarted if it's already down."
		} else if strings.Contains(err.Error(), "executable file not found") {
			errorMessage += "\n\nPossible reasons:\n- 'supervisorctl' command not found. Please ensure Supervisor is installed and in your system's PATH."
		} else if strings.Contains(err.Error(), "exit status") {
			errorMessage += "\n\nPossible reasons:\n- Supervisor daemon might not be running (try: sudo systemctl start supervisor).\n- Insufficient permissions to execute 'supervisorctl' (try: sudo supervisorctl [action] [service_name])."
		}
		return fmt.Errorf("%s", errorMessage)
	}
	return nil
}

func getSupervisorLogs(serviceName string) (string, error) {
	output, err := executeCommand("supervisorctl", "tail", serviceName)
	if err != nil {
		// [Translated] 处理supervisor未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("supervisorctl not found for logs. Please install supervisor: sudo apt install supervisor")
			return "supervisorctl not found. Please install supervisor on this system.\n\nOn Debian/Ubuntu: sudo apt install supervisor\nOn CentOS/RHEL: sudo yum install supervisor", nil
		}

		// [Translated] 处理其他supervisor错误
		if strings.Contains(err.Error(), "exit status") {
			log.Printf("supervisorctl tail failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for service '%s'.\n\nPossible issues:\n- Supervisor daemon not running (try: sudo systemctl start supervisor)\n- Service name incorrect\n- Insufficient permissions\n\nError: %v", serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] Systemd服务的辅助函数
func listSystemdServices() ([]model.SystemdService, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockSystemdServices(), nil
	}
	output, err := executeCommand("systemctl", "list-units", "--type=service", "--all", "--no-legend", "--plain")
	if err != nil {
		if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("systemctl not found. Please install systemd")
			}
			return getMockSystemdServices(), nil
		}
		return nil, fmt.Errorf("failed to execute systemctl list-units: %w", err)
	}

	var services []model.SystemdService
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 4 {
			continue
		}

		name := parts[0]
		activeStatus := parts[2]
		subStatus := parts[3]

		status := "unknown"
		if activeStatus == "active" && subStatus == "running" {
			status = "running"
		} else if activeStatus == "active" && subStatus == "exited" {
			status = "stopped"
		} else if activeStatus == "inactive" || subStatus == "dead" {
			status = "stopped"
		} else if activeStatus == "failed" {
			status = "failed"
		}

		description := strings.Join(parts[4:], " ")

		service := model.SystemdService{
			Name:        name,
			Status:      status,
			Description: description,
		}

		// [Translated] 获取Systemd服务的详细信息，包括PID和运行时间
		detailOutput, err := executeCommand("systemctl", "show", service.Name)
		if err != nil {
			log.Printf("Failed to get systemctl show for %s: %v", service.Name, err)
			// [Translated] 继续处理下一个服务，即使无法获取详细信息
		} else {
			// log.Printf("systemctl show %s 的输出: %s", service.Name, detailOutput) // 添加此日志
			pidStr := parseSystemdProperty(detailOutput, "MainPID")
			// log.Printf("解析 %s 的 PID: %s", service.Name, pidStr) // 添加此日志
			if pidStr != "" {
				pid, err := strconv.ParseInt(pidStr, 10, 32)
				if err == nil && pid > 0 {
					service.PID = pidStr
					p, err := proc.NewProcess(int32(pid))
					if err == nil {
						// [Translated] 获取CPU使用率
						cpuPercent, err := p.CPUPercent()
						if err == nil {
							service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
						} else {
							log.Printf("Failed to get CPU for PID %d: %v", pid, err)
							service.CPU = "--" // Set to -- if failed
						}

						// [Translated] 获取内存使用情况 (RSS)
						memInfo, err := p.MemoryInfo()
						if err == nil {
							service.Memory = formatBytes(memInfo.RSS)
						} else {
							log.Printf("Failed to get Memory for PID %d: %v", pid, err)
							service.Memory = "--" // Set to -- if failed
						}

						// [Translated] 获取运行时间
						createTime, err := p.CreateTime() // [Translated] 进程创建时间（Unix毫秒）
						if err == nil {
							uptimeSeconds := (time.Now().UnixNano() - createTime*1_000_000) / 1_000_000_000
							service.Uptime = uptimeSeconds
						} else {
							log.Printf("Failed to get CreateTime for PID %d: %v", pid, err)
						}

					} else {
						log.Printf("Failed to create process for PID %d: %v", pid, err)
					}
				} else {
					// log.Printf("服务 %s 的 PID %s 无效", pidStr, service.Name)
				}
			} else {
				// log.Printf("服务 %s 的 MainPID 未找到", service.Name)
			}
		}
		services = append(services, service)
	}
	return services, nil
}

// [Translated] 从systemctl show输出中解析属性值
func parseSystemdProperty(output, property string) string {
	prefix := property + "="
	for _, line := range strings.Split(output, "\n") {
		if strings.HasPrefix(line, prefix) {
			return strings.TrimSpace(strings.TrimPrefix(line, prefix))
		}
	}
	return ""
}

// [Translated] 格式化秒数为人类可读的运行时间
func formatUptime(seconds int64) string {
	if seconds < 0 {
		return "--"
	}
	days := seconds / (60 * 60 * 24)
	hours := (seconds % (60 * 60 * 24)) / (60 * 60)
	minutes := (seconds % (60 * 60)) / 60
	secs := seconds % 60

	parts := []string{}
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d days", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d hours", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d minutes", minutes))
	}
	if secs > 0 || len(parts) == 0 { // Always show seconds if no larger unit, or if it's 0 seconds
		parts = append(parts, fmt.Sprintf("%d seconds", secs))
	}
	return strings.Join(parts, ", ")
}

// parseUptimeStringToSeconds parses a supervisorctl uptime string (e.g., "5 days, 16:11:58" or "0:00:51") into seconds.
func parseUptimeStringToSeconds(uptimeStr string) int64 {
	var totalSeconds int64

	// Handle "days" part
	if strings.Contains(uptimeStr, "days") {
		parts := strings.Split(uptimeStr, "days,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1]) // Remaining part for HH:MM:SS
		}
	} else if strings.Contains(uptimeStr, "day") { // Handle "day" (singular)
		parts := strings.Split(uptimeStr, "day,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1])
		}
	}

	// Handle HH:MM:SS or MM:SS or SS part
	timeParts := strings.Split(uptimeStr, ":")
	var hours, minutes, seconds int64
	var err error

	if len(timeParts) == 3 { // HH:MM:SS
		hours, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[2]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 2 { // MM:SS (common for shorter uptimes)
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 1 { // SS (very short uptimes)
		// Check if it's just seconds (e.g., "51 seconds")
		sParts := strings.Fields(uptimeStr)
		if len(sParts) == 2 && sParts[1] == "seconds" {
			seconds, err = strconv.ParseInt(strings.TrimSpace(sParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		} else {
			// If it's just a number, assume it's seconds
			seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		}
	} else {
		return 0 // Unknown format
	}

	totalSeconds += hours*60*60 + minutes*60 + seconds
	return totalSeconds
}

// 获取模拟的 Systemd 服务数据（用于测试）
func getMockSystemdServices() []model.SystemdService {
	return []model.SystemdService{
		{
			Name:        "nginx.service",
			Status:      "running",
			Description: "A high performance web server and a reverse proxy server",
			PID:         "12345",
			Uptime:      parseUptimeStringToSeconds("1 day, 5 hours, 30 minutes, 15 seconds"),
			Memory:      "64 MB",
			CPU:         "3.50%",
		},
		{
			Name:        "mysql.service",
			Status:      "running",
			Description: "MySQL Community Server",
			PID:         "67890",
			Uptime:      parseUptimeStringToSeconds("2 days, 10 hours, 0 minutes, 0 seconds"),
			Memory:      "256 MB",
			CPU:         "8.10%",
		},
		{
			Name:        "redis.service",
			Status:      "running",
			Description: "Advanced key-value store",
			PID:         "11223",
			Uptime:      parseUptimeStringToSeconds("12 hours, 45 minutes, 30 seconds"),
			Memory:      "32 MB",
			CPU:         "1.20%",
		},
		{
			Name:        "postgresql.service",
			Status:      "stopped",
			Description: "PostgreSQL database server",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
		{
			Name:        "apache2.service",
			Status:      "stopped",
			Description: "The Apache HTTP Server",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
		{
			Name:        "docker.service",
			Status:      "running",
			Description: "Docker Application Container Engine",
			PID:         "98765",
			Uptime:      parseUptimeStringToSeconds("3 days, 2 hours, 10 minutes, 5 seconds"),
			Memory:      "128 MB",
			CPU:         "6.70%",
		},
		{
			Name:        "ssh.service",
			Status:      "running",
			Description: "OpenBSD Secure Shell server",
			PID:         "54321",
			Uptime:      parseUptimeStringToSeconds("1 day, 0 hours, 0 minutes, 0 seconds"),
			Memory:      "10 MB",
			CPU:         "0.10%",
		},
		{
			Name:        "fail2ban.service",
			Status:      "failed",
			Description: "Fail2Ban Service",
			PID:         "",
			Uptime:      0, // Failed services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
		{
			Name:        "elasticsearch.service",
			Status:      "stopped",
			Description: "Elasticsearch",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
		{
			Name:        "kibana.service",
			Status:      "stopped",
			Description: "Kibana",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
	}
}

func controlSystemdService(serviceName, action string) error {
	output, err := executeCommand("systemctl", action, serviceName)
	if err != nil {
		return fmt.Errorf("systemctl %s %s failed: %w, output: %s", action, serviceName, err, output)
	}
	return nil
}

func getSystemdLogs(serviceName string) (string, error) {
	output, err := executeCommand("journalctl", "-u", serviceName, "--no-pager", "-n", "100")
	if err != nil {
		// [Translated] 处理journalctl未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("journalctl not found for logs. This system may not use systemd")
			return "journalctl not found. This system may not use systemd.\n\nSystemd is typically available on:\n- Modern Linux distributions (Ubuntu 15.04+, CentOS 7+, Debian 8+)\n- Not available on Windows or older Linux systems", nil
		}

		// [Translated] 处理其他systemd错误
		if strings.Contains(err.Error(), "exit status") {
			log.Printf("journalctl failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for service '%s'.\n\nPossible issues:\n- Service name incorrect\n- Insufficient permissions (try: sudo journalctl -u %s)\n- Service not managed by systemd\n\nError: %v", serviceName, serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] Docker服务的辅助函数
func listDockerServices() ([]model.DockerService, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockDockerServices(), nil
	}
	output, err := executeCommand("docker", "ps", "-a", "--format", "{{.ID}}\t{{.Names}}\t{{.Status}}")
	if err != nil {
		// [Translated] 在 Windows 环境下提供模拟数据用于测试
		if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("docker not found. Please install docker")
			}
			return getMockDockerServices(), nil
		}
		return nil, fmt.Errorf("failed to execute docker ps: %w", err)
	}

	var services []model.DockerService
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Split(line, "\t")
		if len(parts) < 3 { // Updated to 3 parts: ID, Name, Status
			continue
		}

		containerID := parts[0]
		name := parts[1]
		rawStatus := parts[2]

		status := "unknown"
		if strings.HasPrefix(rawStatus, "Up") {
			status = "running"
		} else if strings.HasPrefix(rawStatus, "Exited") || strings.HasPrefix(rawStatus, "Created") {
			status = "stopped"
		}

		service := model.DockerService{
			Name:        name,
			Status:      status,
			Description: rawStatus, // Keep raw status for description
		}

		// [Translated] 获取Docker容器的详细信息，包括PID和运行时间
		// [Translated] 从rawStatus中解析Uptime
		service.Uptime = parseDockerUptimeToSeconds(rawStatus)

		inspectOutput, err := executeCommand("docker", "inspect", containerID)
		if err != nil {
			log.Printf("Failed to inspect docker container %s: %v", containerID, err)
			// [Translated] 继续处理下一个服务，即使无法获取详细信息
		} else {
			var inspectData []struct {
				State struct {
					Pid int `json:"Pid"`
				} `json:"State"`
			}
			err := json.Unmarshal([]byte(inspectOutput), &inspectData)
			if err != nil || len(inspectData) == 0 {
				log.Printf("Failed to parse docker inspect output for %s: %v", containerID, err)
			} else {
				pid := inspectData[0].State.Pid
				if pid > 0 {
					service.PID = fmt.Sprintf("%d", pid)
					p, err := proc.NewProcess(int32(pid))
					if err == nil {
						// [Translated] 获取CPU使用率
						cpuPercent, err := p.CPUPercent()
						if err == nil {
							service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
						} else {
							log.Printf("Failed to get CPU for PID %d: %v", pid, err)
							service.CPU = "--" // Set to -- if failed
						}

						// [Translated] 获取内存使用情况 (RSS)
						memInfo, err := p.MemoryInfo()
						if err == nil {
							service.Memory = formatBytes(memInfo.RSS)
						} else {
							log.Printf("Failed to get Memory for PID %d: %v", pid, err)
							service.Memory = "--" // Set to -- if failed
						}
					} else {
						log.Printf("Failed to create process for PID %d: %v", pid, err)
					}
				} else {
					// log.Printf("容器 %s 的 PID 未找到", containerID)
				}
			}
		}
		services = append(services, service)
	}
	return services, nil
}

// 获取模拟的 Docker 服务数据（用于测试）
func getMockDockerServices() []model.DockerService {
	return []model.DockerService{
		{
			Name:        "web-app",
			Status:      "running",
			Description: "Up 2 hours",
			PID:         "12345",
			Uptime:      parseUptimeStringToSeconds("2 hours, 30 minutes, 15 seconds"),
			Memory:      "128 MB",
			CPU:         "3.50%",
		},
		{
			Name:        "redis-cache",
			Status:      "running",
			Description: "Up 5 days",
			PID:         "67890",
			Uptime:      parseUptimeStringToSeconds("5 days, 10 hours, 0 minutes, 0 seconds"),
			Memory:      "256 MB",
			CPU:         "8.10%",
		},
		{
			Name:        "postgres-db",
			Status:      "running",
			Description: "Up 3 days",
			PID:         "11223",
			Uptime:      parseUptimeStringToSeconds("3 days, 12 hours, 45 minutes, 30 seconds"),
			Memory:      "32 MB",
			CPU:         "1.20%",
		},
		{
			Name:        "nginx-proxy",
			Status:      "running",
			Description: "Up 1 week",
			PID:         "98765",
			Uptime:      parseUptimeStringToSeconds("7 days, 2 hours, 10 minutes, 5 seconds"),
			Memory:      "64 MB",
			CPU:         "6.70%",
		},
		{
			Name:        "monitoring-grafana",
			Status:      "stopped",
			Description: "Exited (0) 2 hours ago",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
		{
			Name:        "log-collector",
			Status:      "stopped",
			Description: "Exited (1) 30 minutes ago",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
		},
	}
}

func parseDockerUptimeToSeconds(status string) int64 {
	// Example status: "Up 22 minutes", "Up 5 days", "Exited (0) 2 hours ago"
	if !strings.HasPrefix(status, "Up ") {
		return 0 // Not a running container, or uptime not available in this format
	}

	status = strings.TrimPrefix(status, "Up ")
	parts := strings.Fields(status)

	if len(parts) < 2 {
		return 0 // Not enough parts to parse (e.g., just "Up")
	}

	valueStr := parts[0]
	unit := parts[1]

	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		log.Printf("Failed to parse uptime value '%s': %v", valueStr, err)
		return 0
	}

	switch {
	case strings.HasPrefix(unit, "second"):
		return value
	case strings.HasPrefix(unit, "minute"):
		return value * 60
	case strings.HasPrefix(unit, "hour"):
		return value * 60 * 60
	case strings.HasPrefix(unit, "day"):
		return value * 24 * 60 * 60
	case strings.HasPrefix(unit, "week"):
		return value * 7 * 24 * 60 * 60
	case strings.HasPrefix(unit, "month"): // Approximate
		return value * 30 * 24 * 60 * 60
	case strings.HasPrefix(unit, "year"): // Approximate
		return value * 365 * 24 * 60 * 60
	default:
		log.Printf("Unknown uptime unit '%s' in status: %s", unit, status)
		return 0
	}
}

func controlDockerService(serviceName, action string) error {
	switch action {
	case "start":
		output, err := executeCommand("docker", "start", serviceName)
		if err != nil {
			return fmt.Errorf("docker start %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	case "stop":
		output, err := executeCommand("docker", "stop", serviceName)
		if err != nil {
			return fmt.Errorf("docker stop %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	case "restart":
		output, err := executeCommand("docker", "restart", serviceName)
		if err != nil {
			return fmt.Errorf("docker restart %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	default:
		return fmt.Errorf("invalid action: %s", action)
	}
}

func getDockerLogs(serviceName string) (string, error) {
	output, err := executeCommand("docker", "logs", "--tail", "100", serviceName)
	if err != nil {
		// [Translated] 处理docker未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("docker not found for logs. Please install Docker")
			return "Docker not found. Please install Docker on this system.\n\nInstallation guides:\n- Ubuntu/Debian: sudo apt install docker.io\n- CentOS/RHEL: sudo yum install docker\n- Windows: Download Docker Desktop", nil
		}

		// [Translated] 处理其他docker错误
		if strings.Contains(err.Error(), "exit status") || strings.Contains(err.Error(), "No such container") {
			log.Printf("docker logs failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for container '%s'.\n\nPossible issues:\n- Container name incorrect\n- Container doesn't exist (check: docker ps -a)\n- Docker daemon not running (try: sudo systemctl start docker)\n- Insufficient permissions (try: sudo docker logs %s)\n\nError: %v", serviceName, serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] 连接管理器方法
func (cm *ConnectionManager) AddConnection(connID string, conn *websocket.Conn, userID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.connections[connID] = &FrontendConnection{
		Conn:         conn,
		UserID:       userID,
		SubscribedTo: make(map[int]bool),
		LastPing:     time.Now(),
	}
}

func (cm *ConnectionManager) RemoveConnection(connID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		conn.Conn.Close()
		delete(cm.connections, connID)
	}
}

func (cm *ConnectionManager) Subscribe(connID string, serverID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		conn.SubscribedTo[serverID] = true
	}
}

func (cm *ConnectionManager) Unsubscribe(connID string, serverID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		delete(conn.SubscribedTo, serverID)
	}
}

func (cm *ConnectionManager) BroadcastToSubscribers(serverID int, message FrontendWSMessage) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	for _, conn := range cm.connections {
		if conn.SubscribedTo[serverID] {
			err := conn.Conn.WriteJSON(message)
			if err != nil {
				log.Printf("Failed to send message to connection: %v", err)
			}
		}
	}
}

func (cm *ConnectionManager) SendToConnection(connID string, message FrontendWSMessage) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.connections[connID]; exists {
		return conn.Conn.WriteJSON(message)
	}
	return fmt.Errorf("connection not found")
}

// [Translated] 前端WebSocket处理程序
func handleFrontendWebSocket(w http.ResponseWriter, r *http.Request) {
	// [Translated] 在WebSocket升级前验证认证
	// [Translated] Authentication for WebSocket will be handled by Hertz middleware
	// during the upgrade process. For now, we proceed with the upgrade directly.
	// The token validation logic for WebSocket connections will be integrated
	// into the Hertz context during the WebSocket migration phase.

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Frontend WebSocket upgrade failed: %v", err)
		return
	}

	connID := fmt.Sprintf("%s_%d", r.RemoteAddr, time.Now().UnixNano())
	log.Printf("Frontend client connected: %s (ID: %s)", r.RemoteAddr, connID)

	// [Translated] 处理连接清理
	defer func() {
		frontendConnManager.RemoveConnection(connID)
		log.Printf("Frontend client disconnected: %s", connID)
	}()

	// [Translated] 处理传入消息
	for {
		var msg FrontendWSMessage
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Frontend WebSocket error: %v", err)
			}
			break
		}

		// [Translated] 处理不同消息类型
		switch msg.Type {
		case "auth":
			handleFrontendAuth(connID, conn, msg)
		case "subscribe":
			handleFrontendSubscribe(connID, msg)
		case "unsubscribe":
			handleFrontendUnsubscribe(connID, msg)
		case "get_system_stats":
			handleFrontendGetSystemStats(connID, msg)
		case "get_service_list":
			handleFrontendGetServiceList(connID, msg)
		case "service_control":
			handleFrontendServiceControl(connID, msg)
		case "ping":
			handleFrontendPing(connID)
		default:
			log.Printf("Unknown message type: %s", msg.Type)
		}
	}
}

// [Translated] 前端WebSocket消息处理程序
func handleFrontendAuth(connID string, conn *websocket.Conn, msg FrontendWSMessage) {
	// [Translated] 由于前端不再发送token，我们假设WebSocket连接已经通过HTTP认证
	// 在WebSocket升级时，服务器已经验证了cookie
	// 这里我们直接接受认证，因为WebSocket连接本身已经建立了

	// [Translated] 添加已认证连接
	frontendConnManager.AddConnection(connID, conn, "authenticated_user")

	response := FrontendWSMessage{
		Type:      "auth_response",
		Data:      map[string]string{"status": "authenticated", "user": "authenticated_user"},
		Timestamp: time.Now(),
	}
	conn.WriteJSON(response)
}

func handleFrontendSubscribe(connID string, msg FrontendWSMessage) {
	if msg.ServerID > 0 {
		frontendConnManager.Subscribe(connID, msg.ServerID)

		response := FrontendWSMessage{
			Type:      "subscribe_response",
			ServerID:  msg.ServerID,
			Data:      map[string]string{"status": "subscribed"},
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
	}
}

func handleFrontendUnsubscribe(connID string, msg FrontendWSMessage) {
	if msg.ServerID > 0 {
		frontendConnManager.Unsubscribe(connID, msg.ServerID)

		response := FrontendWSMessage{
			Type:      "unsubscribe_response",
			ServerID:  msg.ServerID,
			Data:      map[string]string{"status": "unsubscribed"},
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
	}
}

func handleFrontendGetSystemStats(connID string, msg FrontendWSMessage) {
	// [Translated] 获取当前系统统计信息
	stats, err := getCurrentSystemStats()
	if err != nil {
		response := FrontendWSMessage{
			Type:      "system_stats_response",
			Error:     err.Error(),
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	response := FrontendWSMessage{
		Type:      "system_stats_response",
		Data:      stats,
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

func handleFrontendGetServiceList(connID string, msg FrontendWSMessage) {
	// [Translated] 从消息数据中提取服务类型
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     "Invalid request data",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	serviceType, ok := data["serviceType"].(string)
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     "Missing serviceType",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	var services interface{}
	var err error

	switch serviceType {
	case "supervisor":
		services, err = listSupervisorServices()
	case "systemd":
		services, err = listSystemdServices()
	case "docker":
		services, err = listDockerServices()
	default:
		err = fmt.Errorf("invalid service type: %s", serviceType)
	}

	if err != nil {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     err.Error(),
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	response := FrontendWSMessage{
		Type:      "service_list_response",
		Data:      map[string]interface{}{"services": services, "serviceType": serviceType},
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

func handleFrontendServiceControl(connID string, msg FrontendWSMessage) {
	// [Translated] 从消息中提取服务控制数据
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_control_response",
			Error:     "Invalid request data",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	serviceType, _ := data["serviceType"].(string)
	serviceName, _ := data["serviceName"].(string)
	action, _ := data["action"].(string)

	if serviceType == "" || serviceName == "" || action == "" {
		response := FrontendWSMessage{
			Type:      "service_control_response",
			Error:     "Missing required fields: serviceType, serviceName, action",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	var err error
	switch serviceType {
	case "supervisor":
		err = controlSupervisorService(serviceName, action)
	case "systemd":
		err = controlSystemdService(serviceName, action)
	case "docker":
		err = controlDockerService(serviceName, action)
	default:
		err = fmt.Errorf("invalid service type: %s", serviceType)
	}

	response := FrontendWSMessage{
		Type: "service_control_response",
		Data: map[string]interface{}{
			"serviceType": serviceType,
			"serviceName": serviceName,
			"action":      action,
			"success":     err == nil,
		},
		Timestamp: time.Now(),
	}

	if err != nil {
		response.Error = err.Error()
	}

	frontendConnManager.SendToConnection(connID, response)

	// [Translated] 如果操作成功，则广播最新的服务列表
	if err == nil {
		var updatedServices interface{}
		var getErr error
		// [Translated] 从原始消息中获取ServerID，这对于广播是必要的
		serverID, ok := msg.Data.(map[string]interface{})["serverID"].(float64) // Assuming serverID is sent as float64 from JS
		if !ok {
			log.Printf("ServerID not found or invalid in service control message data for connID %s", connID)
			return
		}

		switch serviceType {
		case "supervisor":
			updatedServices, getErr = listSupervisorServices()
		case "systemd":
			updatedServices, getErr = listSystemdServices()
		case "docker":
			updatedServices, getErr = listDockerServices()
		}

		if getErr != nil {
			log.Printf("Failed to get updated service list after control operation for %s %s: %v", serviceType, serviceName, getErr)
		} else {
			broadcastMessage := FrontendWSMessage{
				Type:      "service_list_update", // A new type for service list updates
				ServerID:  int(serverID),         // Convert float64 to int
				Data:      map[string]interface{}{"services": updatedServices, "serviceType": serviceType},
				Timestamp: time.Now(),
			}
			// [Translated] 广播给所有订阅了该ServerID的前端连接
			frontendConnManager.BroadcastToSubscribers(int(serverID), broadcastMessage)
			log.Printf("Broadcasted updated %s service list for ServerID %d after control operation.", serviceType, int(serverID))
		}
	}
}

func handleFrontendPing(connID string) {
	response := FrontendWSMessage{
		Type:      "pong",
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

// [Translated] 实时数据广播
func startRealTimeDataBroadcast() {
	ticker := time.NewTicker(1 * time.Second) // [Translated] 每1秒广播一次以进行实时监控
	defer ticker.Stop()

	log.Println("Started real-time data broadcasting (1 second interval)")

	for range ticker.C {
		broadcastSystemStats()
	}
}

// [Translated] getLatestClientStats从数据库中检索特定客户端的最新统计信息
func getLatestClientStats(serverID int) (*model.StatusInfo, bool) {
	// [Translated] 首先检查缓存
	serverStatusCache.RLock()
	if cachedStats, ok := serverStatusCache.data[serverID]; ok {
		serverStatusCache.RUnlock()
		return cachedStats, true
	}
	serverStatusCache.RUnlock()

	statusInfo, found := serverStatusRepo.GetLatestClientStats(serverID)
	if !found {
		// Return a default StatusInfo and false for 'found'
		return &model.StatusInfo{
			MemTotal: 1, // Avoid division by zero in frontend if MemTotal is 0
		}, false
	}

	// [Translated] 更新缓存
	serverStatusCache.Lock()
	serverStatusCache.data[serverID] = statusInfo
	serverStatusCache.Unlock()

	return statusInfo, true
}

func broadcastSystemStats() {
	// Collect all server IDs from the config
	var serverIDs []int
	for _, s := range ServerConfigGlobal.Servers {
		serverIDs = append(serverIDs, s.ID)
	}

	// Fetch all ServerStatus records in a single query
	var allServerStatuses []model.ServerStatus
	allServerStatuses, err := serverStatusRepo.GetAllServerStatuses(serverIDs)
	if err != nil {
		log.Printf("Database error fetching all ServerStatus for broadcast: %v", err)
		// Continue with available data or send empty stats
	}

	// Create a map for quick lookup
	serverStatusMap := make(map[int]model.ServerStatus)
	for _, ss := range allServerStatuses {
		serverStatusMap[ss.ID] = ss
	}

	for _, s := range ServerConfigGlobal.Servers { // Iterate over configured servers
		stats := &model.StatusInfo{MemTotal: 1} // Default empty stats, avoid division by zero
		if ss, ok := serverStatusMap[s.ID]; ok {
			stats = &model.StatusInfo{
				CPU:         ss.StatusCPU,
				MemUsed:     ss.StatusMemUsed,
				MemTotal:    ss.StatusMemTotal,
				DiskUsed:    ss.StatusDiskUsed,
				DiskTotal:   ss.StatusDiskTotal,
				NetInSpeed:  ss.StatusNetInSpeed,
				NetOutSpeed: ss.StatusNetOutSpeed,
				Uptime:      ss.StatusUptime,
				Load1:       ss.StatusLoad1,
				Load5:       ss.StatusLoad5,
				Load15:      ss.StatusLoad15,
			}
		} else {
			log.Printf("No real-time stats found for server ID %d (%s), broadcasting default/empty stats.", s.ID, s.Name)
		}

		message := FrontendWSMessage{
			Type:      "system_stats_broadcast",
			ServerID:  s.ID,
			Data:      stats,
			Timestamp: time.Now(),
		}
		frontendConnManager.BroadcastToSubscribers(s.ID, message)
	}
}

// 获取可执行文件的目录
func getExecutableDir() string {
	execPath, err := os.Executable()
	if err != nil {
		log.Fatalf("Failed to get executable path: %v", err)
	}
	return filepath.Dir(execPath)
}

// 获取相对于可执行文件的文件路径
func getFilePath(filename string) string {
	execDir := getExecutableDir()
	return filepath.Join(execDir, filename)
}

// [Translated] loadServerConfig 从指定文件路径加载服务器配置
func loadServerConfig(filePath string) (*ServerConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read server config file %s: %v", filePath, err)
	}
	var config ServerConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse server config file %s: %v", filePath, err)
	}
	return &config, nil
}

// [Translated] loadClientConfig 从指定文件路径加载客户端配置
func loadClientConfig(filePath string) (*ClientConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read client config file %s: %v", filePath, err)
	}
	var config ClientConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse client config file %s: %v", filePath, err)
	}
	return &config, nil
}
